# TPC-DS?????? - PB?????
# ???????????PB???????

# ===== Spark?? =====
spark.master=yarn
spark.app.name=TPC-DS Iceberg Benchmark - PB Scale
spark.executor.memory=16g
spark.executor.cores=8
spark.executor.instances=100
spark.executor.memoryFraction=0.8

# ??????
spark.dynamicAllocation.enabled=true
spark.dynamicAllocation.minExecutors=50
spark.dynamicAllocation.maxExecutors=200
spark.dynamicAllocation.initialExecutors=100

# SQL???? - PB???
spark.sql.adaptive.enabled=true
spark.sql.adaptive.advisoryPartitionSizeInBytes=256MB
spark.sql.adaptive.coalescePartitions.enabled=true
spark.sql.adaptive.coalescePartitions.minPartitionNum=1
spark.sql.adaptive.coalescePartitions.initialPartitionNum=1000

# ????CBO??
spark.sql.cbo.enabled=true
spark.sql.cbo.planStats.enabled=true
spark.sql.cbo.joinReorder.enabled=true
spark.sql.cbo.histogram.enabled=true

# ????????
spark.serializer=org.apache.spark.serializer.KryoSerializer
spark.sql.execution.arrow.pyspark.enabled=true
spark.sql.execution.arrow.maxRecordsPerBatch=10000

# ===== Iceberg?? =====
iceberg.enabled=true
iceberg.default.catalog=hadoop_pb
iceberg.format.version=2

# Hadoop Catalog?? - PB???
iceberg.catalog.hadoop_pb.type=hadoop
iceberg.catalog.hadoop_pb.warehouse=hdfs://namenode:9000/warehouse/iceberg_pb
iceberg.catalog.hadoop_pb.property.write.format.default=parquet
iceberg.catalog.hadoop_pb.property.write.parquet.compression-codec=zstd

# Hive Catalog??????
iceberg.catalog.hive_pb.type=hive
iceberg.catalog.hive_pb.warehouse=hdfs://namenode:9000/warehouse/hive_pb
iceberg.catalog.hive_pb.uri=thrift://hive-metastore:9083
iceberg.catalog.hive_pb.property.write.format.default=parquet
iceberg.catalog.hive_pb.property.write.parquet.compression-codec=zstd

# ????? - PB???
iceberg.table.property.write.target-file-size-bytes=1073741824
iceberg.table.property.write.parquet.row-group-size-bytes=134217728
iceberg.table.property.write.parquet.page-size-bytes=1048576
iceberg.table.property.write.distribution-mode=hash
iceberg.table.property.write.delete.mode=merge-on-read
iceberg.table.property.write.update.mode=merge-on-read
iceberg.table.property.write.merge.mode=merge-on-read

# ???????
iceberg.table.property.write.parquet.dict-size-bytes=2097152
iceberg.table.property.write.parquet.bloom-filter-enabled.col1=true
iceberg.table.property.write.parquet.bloom-filter-enabled.col2=true

# ===== ?????? =====
datagen.output.location=hdfs://namenode:9000/tpcds-data-pb
datagen.scale.factor=10000
datagen.format=parquet
datagen.overwrite=true
datagen.partition.tables=true
datagen.use.double.for.decimal=false
datagen.use.string.for.char=false
datagen.cluster.by.partition.columns=true
datagen.filter.out.null.partition.values=true
datagen.num.partitions=10000

# ===== ?????? =====
benchmark.data.location=hdfs://namenode:9000/tpcds-data-pb
benchmark.iterations=3
benchmark.warmup.iterations=1
benchmark.output.format=json
benchmark.output.location=hdfs://namenode:9000/benchmark-results

# ??????
spark.eventLog.enabled=true
spark.eventLog.dir=hdfs://namenode:9000/spark-events
spark.history.fs.logDirectory=hdfs://namenode:9000/spark-events

# ???IO??
spark.network.timeout=800s
spark.sql.broadcastTimeout=36000
spark.sql.adaptive.localShuffleReader.enabled=true
spark.sql.adaptive.skewJoin.enabled=true
