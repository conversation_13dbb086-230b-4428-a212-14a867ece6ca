# TPC-DS?????? - Hive Catalog????
# ?????Hive Metastore?????

# ===== Spark?? =====
spark.master=yarn
spark.app.name=TPC-DS Iceberg Benchmark - Hive Production
spark.executor.memory=8g
spark.executor.cores=4
spark.executor.instances=50
spark.executor.memoryFraction=0.8

# SQL????
spark.sql.adaptive.enabled=true
spark.sql.adaptive.advisoryPartitionSizeInBytes=128MB
spark.sql.adaptive.coalescePartitions.enabled=true
spark.sql.adaptive.coalescePartitions.minPartitionNum=1

# CBO??
spark.sql.cbo.enabled=true
spark.sql.cbo.planStats.enabled=true
spark.sql.cbo.joinReorder.enabled=true
spark.sql.cbo.histogram.enabled=true

# ===== Iceberg?? =====
iceberg.enabled=true
iceberg.default.catalog=hive_prod
iceberg.format.version=2

# Hive Catalog??
iceberg.catalog.hive_prod.type=hive
iceberg.catalog.hive_prod.warehouse=hdfs://namenode:9000/warehouse/hive
iceberg.catalog.hive_prod.uri=thrift://hive-metastore:9083
iceberg.catalog.hive_prod.property.write.format.default=parquet
iceberg.catalog.hive_prod.property.write.parquet.compression-codec=zstd

# Hadoop Catalog??????
iceberg.catalog.hadoop_prod.type=hadoop
iceberg.catalog.hadoop_prod.warehouse=hdfs://namenode:9000/warehouse/iceberg
iceberg.catalog.hadoop_prod.property.write.format.default=parquet
iceberg.catalog.hadoop_prod.property.write.parquet.compression-codec=zstd

# ?????
iceberg.table.property.write.target-file-size-bytes=268435456
iceberg.table.property.write.parquet.row-group-size-bytes=67108864
iceberg.table.property.write.parquet.page-size-bytes=1048576
iceberg.table.property.write.distribution-mode=hash

# ===== ?????? =====
datagen.output.location=hdfs://namenode:9000/tpcds-data
datagen.scale.factor=1000
datagen.format=parquet
datagen.overwrite=true
datagen.partition.tables=true
datagen.use.double.for.decimal=false
datagen.use.string.for.char=false
datagen.cluster.by.partition.columns=true
datagen.filter.out.null.partition.values=true
datagen.num.partitions=1000

# ===== ?????? =====
benchmark.data.location=hdfs://namenode:9000/tpcds-data
benchmark.iterations=2
benchmark.warmup.iterations=1
benchmark.output.format=json
benchmark.output.location=hdfs://namenode:9000/benchmark-results

# Hive?????
spark.sql.hive.metastore.version=3.1.2
spark.sql.hive.metastore.jars=builtin
