# TPC-DS?????? - ??????
# ?????????????

# ===== Spark?? =====
spark.master=local[2]
spark.app.name=TPC-DS Iceberg Benchmark - Local
spark.executor.memory=2g
spark.executor.cores=1
spark.executor.memoryFraction=0.8

# SQL????
spark.sql.adaptive.enabled=true
spark.sql.adaptive.advisoryPartitionSizeInBytes=64MB
spark.sql.adaptive.coalescePartitions.enabled=true
spark.sql.adaptive.coalescePartitions.minPartitionNum=1

# CBO???????????????
spark.sql.cbo.enabled=false
spark.sql.cbo.planStats.enabled=false
spark.sql.cbo.joinReorder.enabled=false
spark.sql.cbo.histogram.enabled=false

# ===== Iceberg?? =====
iceberg.enabled=true
iceberg.default.catalog=hadoop_local
iceberg.format.version=2

# Hadoop Catalog??
iceberg.catalog.hadoop_local.type=hadoop
iceberg.catalog.hadoop_local.warehouse=file:///tmp/iceberg_warehouse
iceberg.catalog.hadoop_local.property.write.format.default=parquet
iceberg.catalog.hadoop_local.property.write.parquet.compression-codec=snappy

# ?????
iceberg.table.property.write.target-file-size-bytes=67108864
iceberg.table.property.write.parquet.row-group-size-bytes=33554432
iceberg.table.property.write.parquet.page-size-bytes=1048576

# ===== ?????? =====
datagen.output.location=/tmp/tpcds-data
datagen.scale.factor=1
datagen.format=parquet
datagen.overwrite=true
datagen.partition.tables=false
datagen.use.double.for.decimal=false
datagen.use.string.for.char=false
datagen.cluster.by.partition.columns=false
datagen.filter.out.null.partition.values=false
datagen.num.partitions=4
# datagen.table.filter=catalog_sales,store_sales,web_sales

# ===== ?????? =====
benchmark.data.location=/tmp/tpcds-data
benchmark.iterations=1
benchmark.warmup.iterations=0
benchmark.output.format=console
# benchmark.query.filter=q1,q2,q3
# benchmark.output.location=/tmp/benchmark-results
