/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.benchmark.sql

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.execution.benchmark.config.{BenchmarkConfig, CatalogConfig}

/**
 * Catalog管理器
 * 负责配置和管理Iceberg catalogs，支持Hadoop和Hive catalog
 */
class CatalogManager(spark: SparkSession, config: BenchmarkConfig) {

  /**
   * 初始化所有配置的catalogs
   */
  def initializeCatalogs(): Unit = {
    if (config.iceberg.enabled) {
      config.iceberg.catalogs.foreach { case (catalogName, catalogConfig) =>
        configureCatalog(catalogName, catalogConfig)
      }
    }
  }

  /**
   * 配置单个catalog
   */
  private def configureCatalog(catalogName: String, catalogConfig: CatalogConfig): Unit = {
    catalogConfig.catalogType.toLowerCase match {
      case "hadoop" => configureHadoopCatalog(catalogName, catalogConfig)
      case "hive" => configureHiveCatalog(catalogName, catalogConfig)
      case other => throw new IllegalArgumentException(s"不支持的catalog类型: $other")
    }
  }

  /**
   * 配置Hadoop catalog
   */
  private def configureHadoopCatalog(catalogName: String, catalogConfig: CatalogConfig): Unit = {
    // 设置基本的Hadoop catalog配置
    spark.conf.set(s"spark.sql.catalog.$catalogName", "org.apache.iceberg.spark.SparkCatalog")
    spark.conf.set(s"spark.sql.catalog.$catalogName.type", "hadoop")
    spark.conf.set(s"spark.sql.catalog.$catalogName.warehouse", catalogConfig.warehouse)

    // 设置额外的属性
    catalogConfig.properties.foreach { case (key, value) =>
      spark.conf.set(s"spark.sql.catalog.$catalogName.$key", value)
    }

    // 注意：不要同时设置 type 和 catalog-impl，这会导致冲突
    // 使用 type=hadoop 是推荐的新配置方式
  }

  /**
   * 配置Hive catalog
   */
  private def configureHiveCatalog(catalogName: String, catalogConfig: CatalogConfig): Unit = {
    // 设置基本的Hive catalog配置
    spark.conf.set(s"spark.sql.catalog.$catalogName", "org.apache.iceberg.spark.SparkCatalog")
    spark.conf.set(s"spark.sql.catalog.$catalogName.type", "hive")
    spark.conf.set(s"spark.sql.catalog.$catalogName.warehouse", catalogConfig.warehouse)

    // 设置Hive Metastore URI
    catalogConfig.uri.foreach { uri =>
      spark.conf.set(s"spark.sql.catalog.$catalogName.uri", uri)
    }

    // 设置额外的属性
    catalogConfig.properties.foreach { case (key, value) =>
      spark.conf.set(s"spark.sql.catalog.$catalogName.$key", value)
    }
  }

  /**
   * 验证catalog配置
   */
  def validateCatalogs(): Unit = {
    config.iceberg.catalogs.foreach { case (catalogName, catalogConfig) =>
      try {
        // 尝试列出catalog中的数据库来验证连接
        spark.sql(s"SHOW DATABASES IN $catalogName").collect()
        println(s"✓ Catalog '$catalogName' 配置成功")
      } catch {
        case e: Exception =>
          println(s"✗ Catalog '$catalogName' 配置失败: ${e.getMessage}")
          throw e
      }
    }
  }

  /**
   * 创建数据库（如果不存在）
   */
  def createDatabaseIfNotExists(databaseName: String, catalogName: Option[String] = None): Unit = {
    val catalog = catalogName.getOrElse(config.iceberg.defaultCatalog)
    val fullDatabaseName = if (catalog.nonEmpty) s"$catalog.$databaseName" else databaseName
    
    spark.sql(s"CREATE DATABASE IF NOT EXISTS $fullDatabaseName")
  }

  /**
   * 切换到指定的catalog和数据库
   */
  def useCatalogAndDatabase(catalogName: String, databaseName: String = "default"): Unit = {
    if (config.iceberg.enabled) {
      spark.sql(s"USE $catalogName.$databaseName")
    }
  }

  /**
   * 获取catalog信息
   */
  def getCatalogInfo(catalogName: String): Unit = {
    println(s"=== Catalog '$catalogName' 信息 ===")
    
    try {
      // 显示数据库列表
      println("数据库列表:")
      spark.sql(s"SHOW DATABASES IN $catalogName").show(false)
      
      // 显示catalog属性
      println(s"Catalog配置:")
      config.iceberg.catalogs.get(catalogName) match {
        case Some(catalogConfig) =>
          println(s"  类型: ${catalogConfig.catalogType}")
          println(s"  仓库位置: ${catalogConfig.warehouse}")
          catalogConfig.uri.foreach(uri => println(s"  URI: $uri"))
          if (catalogConfig.properties.nonEmpty) {
            println("  属性:")
            catalogConfig.properties.foreach { case (key, value) =>
              println(s"    $key: $value")
            }
          }
        case None =>
          println("  未找到配置信息")
      }
    } catch {
      case e: Exception =>
        println(s"获取catalog信息失败: ${e.getMessage}")
    }
  }

  /**
   * 列出所有配置的catalogs
   */
  def listCatalogs(): Unit = {
    println("=== 已配置的Catalogs ===")
    config.iceberg.catalogs.foreach { case (catalogName, catalogConfig) =>
      println(s"$catalogName (${catalogConfig.catalogType}): ${catalogConfig.warehouse}")
    }
    
    if (config.iceberg.catalogs.nonEmpty) {
      println(s"默认catalog: ${config.iceberg.defaultCatalog}")
    }
  }

  /**
   * 检查catalog是否可用
   */
  def isCatalogAvailable(catalogName: String): Boolean = {
    try {
      spark.sql(s"SHOW DATABASES IN $catalogName").collect()
      true
    } catch {
      case _: Exception => false
    }
  }

  /**
   * 获取推荐的catalog配置
   */
  def getRecommendedCatalogConfig(scenario: String): Map[String, CatalogConfig] = {
    scenario.toLowerCase match {
      case "local" | "development" =>
        Map(
          "hadoop_local" -> CatalogConfig(
            catalogType = "hadoop",
            warehouse = "file:///tmp/iceberg_warehouse",
            properties = Map(
              "write.format.default" -> "parquet",
              "write.parquet.compression-codec" -> "snappy"
            )
          )
        )
      
      case "production" | "cluster" =>
        Map(
          "hadoop_prod" -> CatalogConfig(
            catalogType = "hadoop",
            warehouse = "hdfs://namenode:9000/warehouse/iceberg",
            properties = Map(
              "write.format.default" -> "parquet",
              "write.parquet.compression-codec" -> "zstd",
              "write.target-file-size-bytes" -> "268435456" // 256MB
            )
          ),
          "hive_prod" -> CatalogConfig(
            catalogType = "hive",
            warehouse = "hdfs://namenode:9000/warehouse/hive",
            uri = Some("thrift://hive-metastore:9083"),
            properties = Map(
              "write.format.default" -> "parquet",
              "write.parquet.compression-codec" -> "zstd"
            )
          )
        )
      
      case "petabyte" | "pb-scale" =>
        Map(
          "hadoop_pb" -> CatalogConfig(
            catalogType = "hadoop",
            warehouse = "hdfs://namenode:9000/warehouse/iceberg_pb",
            properties = Map(
              "write.format.default" -> "parquet",
              "write.parquet.compression-codec" -> "zstd",
              "write.target-file-size-bytes" -> "1073741824", // 1GB
              "write.parquet.row-group-size-bytes" -> "134217728", // 128MB
              "write.distribution-mode" -> "hash"
            )
          )
        )
      
      case _ =>
        throw new IllegalArgumentException(s"未知的场景: $scenario")
    }
  }

  /**
   * 应用推荐配置
   */
  def applyRecommendedConfig(scenario: String): Unit = {
    val recommendedConfigs = getRecommendedCatalogConfig(scenario)
    
    println(s"应用 '$scenario' 场景的推荐配置...")
    recommendedConfigs.foreach { case (catalogName, catalogConfig) =>
      configureCatalog(catalogName, catalogConfig)
      println(s"✓ 配置catalog '$catalogName'")
    }
  }

  /**
   * 清理catalog配置
   */
  def cleanupCatalogs(): Unit = {
    config.iceberg.catalogs.keys.foreach { catalogName =>
      try {
        // 移除catalog相关的Spark配置
        val catalogConfKeys = spark.conf.getAll.keys.filter(_.startsWith(s"spark.sql.catalog.$catalogName"))
        catalogConfKeys.foreach { key =>
          // 注意：Spark不提供直接移除配置的方法，这里只是示例
          // 实际使用中可能需要重启SparkSession
        }
      } catch {
        case _: Exception => // 忽略清理错误
      }
    }
  }
}

object CatalogManager {
  
  /**
   * 创建CatalogManager实例
   */
  def apply(spark: SparkSession, config: BenchmarkConfig): CatalogManager = {
    new CatalogManager(spark, config)
  }

  /**
   * 验证catalog配置的有效性
   */
  def validateCatalogConfig(catalogConfig: CatalogConfig): Boolean = {
    catalogConfig.catalogType.toLowerCase match {
      case "hadoop" =>
        catalogConfig.warehouse.nonEmpty
      case "hive" =>
        catalogConfig.warehouse.nonEmpty && catalogConfig.uri.isDefined
      case _ =>
        false
    }
  }
}
