/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.benchmark

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.execution.benchmark.config.{BenchmarkConfig, ConfigManager}
import org.apache.spark.sql.execution.benchmark.sql.{CatalogManager, TableCreator}
import org.apache.spark.internal.Logging

import scala.util.{Failure, Success, Try}

/**
 * 重构后的TPC-DS基准测试类
 * 基于配置文件驱动，支持灵活的测试配置
 */
class TPCDSBenchmark(config: BenchmarkConfig) extends Logging {

  private var spark: SparkSession = _
  private var catalogManager: CatalogManager = _
  private var tableCreator: TableCreator = _

  // TPC-DS表名列表
  private val tables = Seq("catalog_page", "catalog_returns", "customer", "customer_address",
    "customer_demographics", "date_dim", "household_demographics", "inventory", "item",
    "promotion", "store", "store_returns", "catalog_sales", "web_sales", "store_sales",
    "web_returns", "web_site", "reason", "call_center", "warehouse", "ship_mode", "income_band",
    "time_dim", "web_page")

  // TPC-DS查询列表
  private val queriesV1_4 = (1 to 99).map(i => f"q$i%d")
  private val queriesV2_7 = (1 to 99).map(i => f"q$i%d")
  private val nameSuffixForQueriesV2_7 = "-v2.7"

  /**
   * 初始化基准测试环境
   */
  def initialize(): Unit = {
    spark = createSparkSession()
    catalogManager = CatalogManager(spark, config)
    tableCreator = new TableCreator(spark, config)

    // 初始化catalogs
    if (config.iceberg.enabled) {
      catalogManager.initializeCatalogs()
      catalogManager.validateCatalogs()
    }
  }

  /**
   * 创建Spark会话
   */
  private def createSparkSession(): SparkSession = {
    val builder = SparkSession.builder()
      .appName(config.spark.appName)
      .master(config.spark.master)

    // 配置executor
    builder.config("spark.executor.memory", config.spark.executor.memory)
    builder.config("spark.executor.cores", config.spark.executor.cores.toString)
    config.spark.executor.instances.foreach { instances =>
      builder.config("spark.executor.instances", instances.toString)
    }

    // 配置SQL优化
    if (config.spark.sql.adaptive.enabled) {
      builder.config("spark.sql.adaptive.enabled", "true")
      builder.config("spark.sql.adaptive.advisoryPartitionSizeInBytes", 
        config.spark.sql.adaptive.advisoryPartitionSizeInBytes)
      
      if (config.spark.sql.adaptive.coalescePartitions.enabled) {
        builder.config("spark.sql.adaptive.coalescePartitions.enabled", "true")
        builder.config("spark.sql.adaptive.coalescePartitions.minPartitionNum", 
          config.spark.sql.adaptive.coalescePartitions.minPartitionNum.toString)
      }
    }

    // 配置Iceberg
    if (config.iceberg.enabled) {
      builder.config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
      
      config.iceberg.catalogs.foreach { case (catalogName, catalogConfig) =>
        catalogConfig.catalogType.toLowerCase match {
          case "hadoop" =>
            builder.config(s"spark.sql.catalog.$catalogName", "org.apache.iceberg.spark.SparkCatalog")
            builder.config(s"spark.sql.catalog.$catalogName.type", "hadoop")
            builder.config(s"spark.sql.catalog.$catalogName.warehouse", catalogConfig.warehouse)
          case "hive" =>
            builder.config(s"spark.sql.catalog.$catalogName", "org.apache.iceberg.spark.SparkCatalog")
            builder.config(s"spark.sql.catalog.$catalogName.type", "hive")
            builder.config(s"spark.sql.catalog.$catalogName.warehouse", catalogConfig.warehouse)
            catalogConfig.uri.foreach { uri =>
              builder.config(s"spark.sql.catalog.$catalogName.uri", uri)
            }
        }
      }
    }

    // 应用额外的配置
    config.spark.additionalConfigs.foreach { case (key, value) =>
      builder.config(key, value)
    }

    builder.getOrCreate()
  }

  /**
   * 设置测试表
   */
  def setupTables(): Map[String, Long] = {
    logInfo("设置TPC-DS测试表...")
    
    val tablesToSetup = if (config.benchmark.queryFilter.nonEmpty) {
      // 如果指定了查询过滤器，只设置相关的表
      tables.filter(table => isTableNeededForQueries(table, config.benchmark.queryFilter))
    } else {
      tables
    }

    val tableSizes = tablesToSetup.map { tableName =>
      setupSingleTable(tableName)
    }.toMap

    logInfo(s"成功设置 ${tableSizes.size} 个表")
    tableSizes
  }

  /**
   * 设置单个表
   */
  private def setupSingleTable(tableName: String): (String, Long) = {
    val startTime = System.currentTimeMillis()
    
    try {
      if (config.iceberg.enabled) {
        setupIcebergTable(tableName)
      } else {
        setupParquetTable(tableName)
      }

      val rowCount = getTableRowCount(tableName)
      val duration = System.currentTimeMillis() - startTime
      
      logInfo(s"✓ 表 $tableName: $rowCount 行 (${duration}ms)")
      tableName -> rowCount
      
    } catch {
      case e: Exception =>
        logError(s"✗ 设置表 $tableName 失败: ${e.getMessage}")
        throw e
    }
  }

  /**
   * 设置Iceberg表
   */
  private def setupIcebergTable(tableName: String): Unit = {
    val catalogName = config.iceberg.defaultCatalog
    val tablePath = s"${config.benchmark.dataLocation}/$tableName"
    
    val dataFrame = spark.read
      .option("vectorization-enabled", true)
      .format("iceberg")
      .load(tablePath)
    
    tableCreator.createTempView(tableName, dataFrame)
  }

  /**
   * 设置Parquet表
   */
  private def setupParquetTable(tableName: String): Unit = {
    val tablePath = s"${config.benchmark.dataLocation}/$tableName"
    
    // 创建外部表
    tableCreator.createExternalTable(tableName, tablePath, "parquet")
  }

  /**
   * 获取表行数
   */
  private def getTableRowCount(tableName: String): Long = {
    spark.table(tableName).count()
  }

  /**
   * 运行基准测试
   */
  def runBenchmark(): Unit = {
    logInfo("开始运行TPC-DS基准测试...")
    
    // 设置表
    val tableSizes = setupTables()
    
    // 配置CBO
    if (config.spark.sql.cbo.enabled) {
      configureCBO()
      analyzeAllTables()
    }

    // 过滤查询
    val queriesV1_4ToRun = filterQueries(queriesV1_4, config.benchmark.queryFilter)
    val queriesV2_7ToRun = filterQueries(queriesV2_7, config.benchmark.queryFilter, nameSuffixForQueriesV2_7)

    if ((queriesV1_4ToRun ++ queriesV2_7ToRun).isEmpty) {
      throw new RuntimeException(s"没有要运行的查询。查询过滤器: ${config.benchmark.queryFilter}")
    }

    logInfo(s"将运行 ${queriesV1_4ToRun.size} 个v1.4查询和 ${queriesV2_7ToRun.size} 个v2.7查询")

    // 运行查询
    runTpcdsQueries(queryLocation = "tpcds", queries = queriesV1_4ToRun, tableSizes)
    runTpcdsQueries(queryLocation = "tpcds-v2.7.0", queries = queriesV2_7ToRun, tableSizes, nameSuffixForQueriesV2_7)

    logInfo("基准测试完成!")
  }

  /**
   * 配置CBO
   */
  private def configureCBO(): Unit = {
    logInfo("配置基于成本的优化器...")
    
    spark.sql(s"SET spark.sql.cbo.enabled=true")
    spark.sql(s"SET spark.sql.cbo.planStats.enabled=${config.spark.sql.cbo.planStatsEnabled}")
    spark.sql(s"SET spark.sql.cbo.joinReorder.enabled=${config.spark.sql.cbo.joinReorderEnabled}")
    spark.sql(s"SET spark.sql.statistics.histogram.enabled=${config.spark.sql.cbo.histogramEnabled}")
  }

  /**
   * 分析所有表的统计信息
   */
  private def analyzeAllTables(): Unit = {
    logInfo("分析表统计信息...")
    val startTime = System.nanoTime()
    
    tables.foreach { tableName =>
      try {
        tableCreator.analyzeTable(tableName, analyzeColumns = true)
        logInfo(s"✓ 分析表 $tableName")
      } catch {
        case e: Exception =>
          logWarning(s"✗ 分析表 $tableName 失败: ${e.getMessage}")
      }
    }
    
    val duration = (System.nanoTime() - startTime) / 1e9
    logInfo(f"表统计信息分析完成，耗时: ${duration}%.2f 秒")
  }

  /**
   * 运行TPC-DS查询
   */
  private def runTpcdsQueries(
      queryLocation: String,
      queries: Seq[String],
      tableSizes: Map[String, Long],
      nameSuffix: String = ""): Unit = {
    
    queries.foreach { queryName =>
      runSingleQuery(queryLocation, queryName, tableSizes, nameSuffix)
    }
  }

  /**
   * 运行单个查询
   */
  private def runSingleQuery(
      queryLocation: String,
      queryName: String,
      tableSizes: Map[String, Long],
      nameSuffix: String = ""): Unit = {
    
    val fullQueryName = s"$queryName$nameSuffix"
    logInfo(s"运行查询: $fullQueryName")
    
    try {
      val queryString = Utils.resourceToString(s"$queryLocation/$queryName.sql")
      
      // 预热运行
      for (i <- 1 to config.benchmark.warmupIterations) {
        logInfo(s"预热运行 $i/${config.benchmark.warmupIterations}: $fullQueryName")
        spark.sql(queryString).count() // 使用count()确保查询执行
      }
      
      // 正式运行
      val results = (1 to config.benchmark.iterations).map { i =>
        logInfo(s"正式运行 $i/${config.benchmark.iterations}: $fullQueryName")
        val startTime = System.nanoTime()
        val result = spark.sql(queryString).count()
        val duration = (System.nanoTime() - startTime) / 1e6 // 转换为毫秒
        
        logInfo(f"查询 $fullQueryName 第 $i 次运行完成: ${duration}%.2f ms, 结果行数: $result")
        duration
      }
      
      // 计算统计信息
      val avgTime = results.sum / results.length
      val minTime = results.min
      val maxTime = results.max
      
      logInfo(f"查询 $fullQueryName 统计: 平均=${avgTime}%.2f ms, 最小=${minTime}%.2f ms, 最大=${maxTime}%.2f ms")
      
    } catch {
      case e: Exception =>
        logError(s"查询 $fullQueryName 执行失败: ${e.getMessage}")
        throw e
    }
  }

  /**
   * 过滤查询
   */
  private def filterQueries(
      origQueries: Seq[String],
      queryFilter: Set[String],
      nameSuffix: String = ""): Seq[String] = {
    
    if (queryFilter.nonEmpty) {
      if (nameSuffix.nonEmpty) {
        origQueries.filter { name => queryFilter.contains(s"$name$nameSuffix") }
      } else {
        origQueries.filter(queryFilter.contains)
      }
    } else {
      origQueries
    }
  }

  /**
   * 判断表是否为指定查询所需
   */
  private def isTableNeededForQueries(tableName: String, queryFilter: Set[String]): Boolean = {
    // 简化实现：假设所有表都可能被查询使用
    // 实际实现中可以解析SQL文件来确定依赖关系
    true
  }

  /**
   * 清理资源
   */
  def cleanup(): Unit = {
    if (spark != null) {
      spark.stop()
    }
  }
}

/**
 * 命令行入口
 */
object TPCDSBenchmark {
  
  def main(args: Array[String]): Unit = {
    val arguments = parseArguments(args)
    
    try {
      // 加载配置
      val config = loadConfiguration(arguments)
      
      // 创建基准测试
      val benchmark = new TPCDSBenchmark(config)
      
      // 初始化
      benchmark.initialize()
      
      // 运行基准测试
      benchmark.runBenchmark()
      
      // 清理
      benchmark.cleanup()
      
    } catch {
      case e: Exception =>
        println(s"基准测试失败: ${e.getMessage}")
        e.printStackTrace()
        System.exit(1)
    }
  }

  /**
   * 解析命令行参数
   */
  private def parseArguments(args: Array[String]): Map[String, String] = {
    val argMap = scala.collection.mutable.Map[String, String]()
    
    var i = 0
    while (i < args.length) {
      args(i) match {
        case "--config" | "-c" =>
          if (i + 1 < args.length) {
            argMap("config") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--config 需要指定配置文件路径")
          }
        case "--template" | "-t" =>
          if (i + 1 < args.length) {
            argMap("template") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--template 需要指定模板名称")
          }
        case "--data-location" =>
          if (i + 1 < args.length) {
            argMap("data-location") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--data-location 需要指定数据路径")
          }
        case "--query-filter" =>
          if (i + 1 < args.length) {
            argMap("query-filter") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--query-filter 需要指定查询列表")
          }
        case "--cbo" =>
          argMap("cbo") = "true"
          i += 1
        case "--iceberg" =>
          argMap("iceberg") = "true"
          i += 1
        case "--help" | "-h" =>
          printUsage()
          System.exit(0)
        case other =>
          throw new IllegalArgumentException(s"未知参数: $other")
      }
    }
    
    argMap.toMap
  }

  /**
   * 加载配置
   */
  private def loadConfiguration(arguments: Map[String, String]): BenchmarkConfig = {
    val configManager = ConfigManager()
    
    val baseConfig = arguments.get("template") match {
      case Some(template) => ConfigManager.loadTemplate(template)
      case None => arguments.get("config") match {
        case Some(configPath) => configManager.loadFromFile(configPath).get
        case None => throw new IllegalArgumentException("必须指定配置文件或模板")
      }
    }

    // 应用命令行参数覆盖
    applyCommandLineOverrides(baseConfig, arguments)
  }

  /**
   * 应用命令行参数覆盖
   */
  private def applyCommandLineOverrides(config: BenchmarkConfig, arguments: Map[String, String]): BenchmarkConfig = {
    var updatedConfig = config
    
    arguments.get("data-location").foreach { location =>
      updatedConfig = updatedConfig.copy(
        benchmark = updatedConfig.benchmark.copy(dataLocation = location)
      )
    }
    
    arguments.get("query-filter").foreach { filter =>
      val querySet = filter.split(",").map(_.trim).toSet
      updatedConfig = updatedConfig.copy(
        benchmark = updatedConfig.benchmark.copy(queryFilter = querySet)
      )
    }
    
    if (arguments.contains("cbo")) {
      updatedConfig = updatedConfig.copy(
        spark = updatedConfig.spark.copy(
          sql = updatedConfig.spark.sql.copy(
            cbo = updatedConfig.spark.sql.cbo.copy(enabled = true)
          )
        )
      )
    }
    
    if (arguments.contains("iceberg")) {
      updatedConfig = updatedConfig.copy(
        iceberg = updatedConfig.iceberg.copy(enabled = true)
      )
    }
    
    updatedConfig
  }

  /**
   * 打印使用说明
   */
  private def printUsage(): Unit = {
    println("""
      |TPC-DS基准测试工具 - 支持Iceberg表格式
      |
      |用法: spark-submit --class TPCDSBenchmark [选项]
      |
      |选项:
      |  -c, --config <文件>          配置文件路径
      |  -t, --template <模板>        使用预定义模板 (local, pb-scale, hive)
      |  --data-location <路径>       测试数据位置
      |  --query-filter <查询>        要运行的查询，如 q1,q2,q3
      |  --cbo                       启用基于成本的优化
      |  --iceberg                   使用Iceberg表
      |  -h, --help                  显示此帮助信息
      |
      |示例:
      |  # 使用本地模板运行所有查询
      |  spark-submit --class TPCDSBenchmark --template local --data-location /tmp/data
      |
      |  # 运行特定查询
      |  spark-submit --class TPCDSBenchmark --template pb-scale --query-filter q1,q2,q3
      |
      |  # 使用配置文件
      |  spark-submit --class TPCDSBenchmark --config benchmark.properties
      |""".stripMargin)
  }
}
