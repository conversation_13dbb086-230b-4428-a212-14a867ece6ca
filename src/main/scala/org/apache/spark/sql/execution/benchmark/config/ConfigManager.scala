/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.benchmark.config

import java.io.{File, FileInputStream, InputStream}
import java.util.Properties
import scala.collection.JavaConverters._
import scala.util.{Failure, Success, Try}

/**
 * 配置管理器
 * 负责从各种来源加载配置：配置文件、环境变量、命令行参数等
 */
class ConfigManager {
  
  /**
   * 从配置文件加载配置
   */
  def loadFromFile(configPath: String): Try[BenchmarkConfig] = {
    Try {
      val file = new File(configPath)
      if (!file.exists()) {
        throw new IllegalArgumentException(s"配置文件不存在: $configPath")
      }
      
      val extension = configPath.toLowerCase.split("\\.").last
      extension match {
        case "properties" => loadFromProperties(new FileInputStream(file))
        case "yaml" | "yml" => loadFromYaml(configPath)
        case _ => throw new IllegalArgumentException(s"不支持的配置文件格式: $extension")
      }
    }
  }

  /**
   * 从资源文件加载配置
   */
  def loadFromResource(resourcePath: String): Try[BenchmarkConfig] = {
    Try {
      val inputStream = getClass.getClassLoader.getResourceAsStream(resourcePath)
      if (inputStream == null) {
        throw new IllegalArgumentException(s"资源文件不存在: $resourcePath")
      }
      
      val extension = resourcePath.toLowerCase.split("\\.").last
      extension match {
        case "properties" => loadFromProperties(inputStream)
        case "yaml" | "yml" => loadFromYamlResource(resourcePath)
        case _ => throw new IllegalArgumentException(s"不支持的配置文件格式: $extension")
      }
    }
  }

  /**
   * 从Properties文件加载配置
   */
  private def loadFromProperties(inputStream: InputStream): BenchmarkConfig = {
    val props = new Properties()
    try {
      props.load(inputStream)
      parsePropertiesConfig(props)
    } finally {
      inputStream.close()
    }
  }

  /**
   * 从YAML文件加载配置（简化实现，实际项目中建议使用专门的YAML库）
   */
  private def loadFromYaml(yamlPath: String): BenchmarkConfig = {
    // 这里简化实现，实际项目中应该使用如SnakeYAML等库
    // 目前先返回默认配置
    ConfigTemplates.localDev()
  }

  private def loadFromYamlResource(resourcePath: String): BenchmarkConfig = {
    // 这里简化实现，实际项目中应该使用如SnakeYAML等库
    ConfigTemplates.localDev()
  }

  /**
   * 解析Properties配置
   */
  private def parsePropertiesConfig(props: Properties): BenchmarkConfig = {
    val propMap = props.asScala.toMap

    // 解析Spark配置
    val sparkConfig = SparkConfig(
      master = propMap.getOrElse("spark.master", "local[*]"),
      appName = propMap.getOrElse("spark.app.name", "TPC-DS Iceberg Benchmark"),
      executor = ExecutorConfig(
        memory = propMap.getOrElse("spark.executor.memory", "4g"),
        cores = propMap.getOrElse("spark.executor.cores", "2").toInt,
        instances = propMap.get("spark.executor.instances").map(_.toInt),
        memoryFraction = propMap.getOrElse("spark.executor.memoryFraction", "0.8").toDouble
      ),
      sql = SqlConfig(
        adaptive = AdaptiveConfig(
          enabled = propMap.getOrElse("spark.sql.adaptive.enabled", "true").toBoolean,
          advisoryPartitionSizeInBytes = propMap.getOrElse("spark.sql.adaptive.advisoryPartitionSizeInBytes", "128MB"),
          coalescePartitions = CoalesceConfig(
            enabled = propMap.getOrElse("spark.sql.adaptive.coalescePartitions.enabled", "true").toBoolean,
            minPartitionNum = propMap.getOrElse("spark.sql.adaptive.coalescePartitions.minPartitionNum", "1").toInt
          )
        ),
        cbo = CboConfig(
          enabled = propMap.getOrElse("spark.sql.cbo.enabled", "false").toBoolean,
          planStatsEnabled = propMap.getOrElse("spark.sql.cbo.planStats.enabled", "false").toBoolean,
          joinReorderEnabled = propMap.getOrElse("spark.sql.cbo.joinReorder.enabled", "false").toBoolean,
          histogramEnabled = propMap.getOrElse("spark.sql.cbo.histogram.enabled", "false").toBoolean
        )
      )
    )

    // 解析Iceberg配置
    val icebergEnabled = propMap.getOrElse("iceberg.enabled", "false").toBoolean
    val icebergConfig = if (icebergEnabled) {
      val catalogConfigs = parseCatalogConfigs(propMap)
      IcebergConfig(
        enabled = true,
        catalogs = catalogConfigs,
        defaultCatalog = propMap.getOrElse("iceberg.default.catalog", "hadoop_prod"),
        tableProperties = parseTableProperties(propMap),
        formatVersion = propMap.getOrElse("iceberg.format.version", "2").toInt
      )
    } else {
      IcebergConfig(enabled = false)
    }

    // 解析数据生成配置
    val datagenConfig = DatagenConfig(
      outputLocation = propMap.getOrElse("datagen.output.location", "/tmp/tpcds-data"),
      scaleFactor = propMap.getOrElse("datagen.scale.factor", "1").toInt,
      format = propMap.getOrElse("datagen.format", "parquet"),
      overwrite = propMap.getOrElse("datagen.overwrite", "false").toBoolean,
      partitionTables = propMap.getOrElse("datagen.partition.tables", "false").toBoolean,
      useDoubleForDecimal = propMap.getOrElse("datagen.use.double.for.decimal", "false").toBoolean,
      useStringForChar = propMap.getOrElse("datagen.use.string.for.char", "false").toBoolean,
      clusterByPartitionColumns = propMap.getOrElse("datagen.cluster.by.partition.columns", "false").toBoolean,
      filterOutNullPartitionValues = propMap.getOrElse("datagen.filter.out.null.partition.values", "false").toBoolean,
      tableFilter = propMap.get("datagen.table.filter").map(_.split(",").map(_.trim).toSet).getOrElse(Set.empty),
      numPartitions = propMap.getOrElse("datagen.num.partitions", "100").toInt
    )

    // 解析基准测试配置
    val benchmarkConfig = BenchmarkTestConfig(
      dataLocation = propMap.getOrElse("benchmark.data.location", "/tmp/tpcds-data"),
      queryFilter = propMap.get("benchmark.query.filter").map(_.split(",").map(_.trim).toSet).getOrElse(Set.empty),
      iterations = propMap.getOrElse("benchmark.iterations", "1").toInt,
      warmupIterations = propMap.getOrElse("benchmark.warmup.iterations", "0").toInt,
      outputFormat = propMap.getOrElse("benchmark.output.format", "console"),
      outputLocation = propMap.get("benchmark.output.location")
    )

    BenchmarkConfig(sparkConfig, icebergConfig, datagenConfig, benchmarkConfig)
  }

  /**
   * 解析catalog配置
   */
  private def parseCatalogConfigs(propMap: Map[String, String]): Map[String, CatalogConfig] = {
    val catalogNames = propMap.keys.filter(_.startsWith("iceberg.catalog."))
      .map(_.split("\\.")(2)).toSet

    catalogNames.map { catalogName =>
      val prefix = s"iceberg.catalog.$catalogName"
      val catalogType = propMap.getOrElse(s"$prefix.type", "hadoop")
      val warehouse = propMap.getOrElse(s"$prefix.warehouse", "/tmp/iceberg_warehouse")
      val uri = propMap.get(s"$prefix.uri")
      
      val properties = propMap.filter { case (key, _) =>
        key.startsWith(s"$prefix.property.")
      }.map { case (key, value) =>
        key.substring(s"$prefix.property.".length) -> value
      }

      catalogName -> CatalogConfig(catalogType, warehouse, uri, properties)
    }.toMap
  }

  /**
   * 解析表属性配置
   */
  private def parseTableProperties(propMap: Map[String, String]): Map[String, String] = {
    propMap.filter { case (key, _) =>
      key.startsWith("iceberg.table.property.")
    }.map { case (key, value) =>
      key.substring("iceberg.table.property.".length) -> value
    }
  }

  /**
   * 合并多个配置源
   */
  def mergeConfigs(configs: BenchmarkConfig*): BenchmarkConfig = {
    configs.reduce { (config1, config2) =>
      BenchmarkConfig(
        spark = mergeSparkConfig(config1.spark, config2.spark),
        iceberg = mergeIcebergConfig(config1.iceberg, config2.iceberg),
        datagen = mergeDatagenConfig(config1.datagen, config2.datagen),
        benchmark = mergeBenchmarkConfig(config1.benchmark, config2.benchmark)
      )
    }
  }

  private def mergeSparkConfig(config1: SparkConfig, config2: SparkConfig): SparkConfig = {
    config2.copy(
      additionalConfigs = config1.additionalConfigs ++ config2.additionalConfigs
    )
  }

  private def mergeIcebergConfig(config1: IcebergConfig, config2: IcebergConfig): IcebergConfig = {
    config2.copy(
      catalogs = config1.catalogs ++ config2.catalogs,
      tableProperties = config1.tableProperties ++ config2.tableProperties
    )
  }

  private def mergeDatagenConfig(config1: DatagenConfig, config2: DatagenConfig): DatagenConfig = {
    // config2优先，但保留config1的非空值
    DatagenConfig(
      outputLocation = if (config2.outputLocation.nonEmpty) config2.outputLocation else config1.outputLocation,
      scaleFactor = config2.scaleFactor,
      format = config2.format,
      overwrite = config2.overwrite,
      partitionTables = config2.partitionTables,
      useDoubleForDecimal = config2.useDoubleForDecimal,
      useStringForChar = config2.useStringForChar,
      clusterByPartitionColumns = config2.clusterByPartitionColumns,
      filterOutNullPartitionValues = config2.filterOutNullPartitionValues,
      tableFilter = if (config2.tableFilter.nonEmpty) config2.tableFilter else config1.tableFilter,
      numPartitions = config2.numPartitions
    )
  }

  private def mergeBenchmarkConfig(config1: BenchmarkTestConfig, config2: BenchmarkTestConfig): BenchmarkTestConfig = {
    BenchmarkTestConfig(
      dataLocation = if (config2.dataLocation.nonEmpty) config2.dataLocation else config1.dataLocation,
      queryFilter = if (config2.queryFilter.nonEmpty) config2.queryFilter else config1.queryFilter,
      iterations = config2.iterations,
      warmupIterations = config2.warmupIterations,
      outputFormat = config2.outputFormat,
      outputLocation = config2.outputLocation.orElse(config1.outputLocation)
    )
  }
}

object ConfigManager {
  def apply(): ConfigManager = new ConfigManager()

  /**
   * 从预定义模板加载配置
   */
  def loadTemplate(templateName: String): BenchmarkConfig = {
    templateName.toLowerCase match {
      case "local" | "local-dev" => ConfigTemplates.localDev()
      case "pb" | "petabyte" | "pb-scale" => ConfigTemplates.petabyteScale()
      case "hive" | "production-hive" => ConfigTemplates.productionHive()
      case _ => throw new IllegalArgumentException(s"未知的配置模板: $templateName")
    }
  }
}
