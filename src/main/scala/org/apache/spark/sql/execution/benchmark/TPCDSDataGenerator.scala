/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.benchmark

import org.apache.spark.sql.{SaveMode, SparkSession}
import org.apache.spark.sql.execution.benchmark.config.{BenchmarkConfig, ConfigManager}
import org.apache.spark.sql.execution.benchmark.sql.{CatalogManager, TableConfigFactory, TableCreator}

/**
 * 重构后的TPC-DS数据生成器
 * 基于配置文件驱动，支持灵活的Iceberg配置
 */
class TPCDSDataGenerator(config: BenchmarkConfig) {

  private var spark: SparkSession = _
  private var catalogManager: CatalogManager = _
  private var tableCreator: TableCreator = _

  /**
   * 初始化Spark会话和相关组件
   */
  def initialize(): Unit = {
    spark = createSparkSession()
    catalogManager = CatalogManager(spark, config)
    tableCreator = new TableCreator(spark, config)

    // 初始化catalogs
    if (config.iceberg.enabled) {
      catalogManager.initializeCatalogs()
      catalogManager.validateCatalogs()
    }
  }

  /**
   * 创建Spark会话
   */
  private def createSparkSession(): SparkSession = {
    val builder = SparkSession.builder()
      .appName(config.spark.appName)
      .master(config.spark.master)

    // 配置executor
    builder.config("spark.executor.memory", config.spark.executor.memory)
    builder.config("spark.executor.cores", config.spark.executor.cores.toString)
    config.spark.executor.instances.foreach { instances =>
      builder.config("spark.executor.instances", instances.toString)
    }

    // 配置SQL优化
    if (config.spark.sql.adaptive.enabled) {
      builder.config("spark.sql.adaptive.enabled", "true")
      builder.config("spark.sql.adaptive.advisoryPartitionSizeInBytes", 
        config.spark.sql.adaptive.advisoryPartitionSizeInBytes)
      
      if (config.spark.sql.adaptive.coalescePartitions.enabled) {
        builder.config("spark.sql.adaptive.coalescePartitions.enabled", "true")
        builder.config("spark.sql.adaptive.coalescePartitions.minPartitionNum", 
          config.spark.sql.adaptive.coalescePartitions.minPartitionNum.toString)
        
        config.spark.sql.adaptive.coalescePartitions.initialPartitionNum.foreach { num =>
          builder.config("spark.sql.adaptive.coalescePartitions.initialPartitionNum", num.toString)
        }
      }
    }

    // 配置CBO
    if (config.spark.sql.cbo.enabled) {
      builder.config("spark.sql.cbo.enabled", "true")
      builder.config("spark.sql.cbo.planStats.enabled", config.spark.sql.cbo.planStatsEnabled.toString)
      builder.config("spark.sql.cbo.joinReorder.enabled", config.spark.sql.cbo.joinReorderEnabled.toString)
      builder.config("spark.sql.statistics.histogram.enabled", config.spark.sql.cbo.histogramEnabled.toString)
    }

    // 配置Iceberg
    if (config.iceberg.enabled) {
      builder.config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
      
      // 根据用户偏好，只配置Hadoop和Hive catalogs
      config.iceberg.catalogs.foreach { case (catalogName, catalogConfig) =>
        catalogConfig.catalogType.toLowerCase match {
          case "hadoop" =>
            builder.config(s"spark.sql.catalog.$catalogName", "org.apache.iceberg.spark.SparkCatalog")
            builder.config(s"spark.sql.catalog.$catalogName.type", "hadoop")
            builder.config(s"spark.sql.catalog.$catalogName.warehouse", catalogConfig.warehouse)
          case "hive" =>
            builder.config(s"spark.sql.catalog.$catalogName", "org.apache.iceberg.spark.SparkCatalog")
            builder.config(s"spark.sql.catalog.$catalogName.type", "hive")
            builder.config(s"spark.sql.catalog.$catalogName.warehouse", catalogConfig.warehouse)
            catalogConfig.uri.foreach { uri =>
              builder.config(s"spark.sql.catalog.$catalogName.uri", uri)
            }
        }
      }
    }

    // 应用额外的配置
    config.spark.additionalConfigs.foreach { case (key, value) =>
      builder.config(key, value)
    }

    builder.getOrCreate()
  }

  /**
   * 生成TPC-DS数据
   */
  def generateData(): Unit = {
    println("开始生成TPC-DS数据...")
    println(s"规模因子: ${config.datagen.scaleFactor}")
    println(s"输出位置: ${config.datagen.outputLocation}")
    println(s"格式: ${if (config.iceberg.enabled) "Iceberg" else config.datagen.format}")

    // 创建Tables实例（复用原有的表定义）
    val tables = new Tables(spark.sqlContext, config.datagen.scaleFactor)

    if (config.iceberg.enabled) {
      generateIcebergData(tables)
    } else {
      generateParquetData(tables)
    }

    println("数据生成完成!")
  }

  /**
   * 生成Iceberg格式数据
   */
  private def generateIcebergData(tables: Tables): Unit = {
    val tableConfigs = if (config.datagen.scaleFactor >= 1000) {
      // PB级场景使用优化配置
      TableConfigFactory.createPetabyteScaleConfigs(config)
    } else {
      TableConfigFactory.createTpcdsTableConfigs(config)
    }

    // 创建数据库
    catalogManager.createDatabaseIfNotExists("tpcds")
    catalogManager.useCatalogAndDatabase(config.iceberg.defaultCatalog, "tpcds")

    tables.genData(
      location = config.datagen.outputLocation,
      format = "iceberg", // 强制使用iceberg格式
      overwrite = config.datagen.overwrite,
      iceberg = true,
      partitionTables = config.datagen.partitionTables,
      useDoubleForDecimal = config.datagen.useDoubleForDecimal,
      useStringForChar = config.datagen.useStringForChar,
      clusterByPartitionColumns = config.datagen.clusterByPartitionColumns,
      filterOutNullPartitionValues = config.datagen.filterOutNullPartitionValues,
      tableFilter = config.datagen.tableFilter,
      numPartitions = config.datagen.numPartitions
    )

    // 优化生成的表
    if (config.datagen.scaleFactor >= 100) {
      println("正在优化Iceberg表...")
      tableConfigs.keys.foreach { tableName =>
        if (config.datagen.tableFilter.isEmpty || config.datagen.tableFilter.contains(tableName)) {
          try {
            tableCreator.optimizeIcebergTable(tableName)
            println(s"✓ 优化表 $tableName")
          } catch {
            case e: Exception =>
              println(s"✗ 优化表 $tableName 失败: ${e.getMessage}")
          }
        }
      }
    }
  }

  /**
   * 生成Parquet格式数据
   */
  private def generateParquetData(tables: Tables): Unit = {
    tables.genData(
      location = config.datagen.outputLocation,
      format = config.datagen.format,
      overwrite = config.datagen.overwrite,
      iceberg = false,
      partitionTables = config.datagen.partitionTables,
      useDoubleForDecimal = config.datagen.useDoubleForDecimal,
      useStringForChar = config.datagen.useStringForChar,
      clusterByPartitionColumns = config.datagen.clusterByPartitionColumns,
      filterOutNullPartitionValues = config.datagen.filterOutNullPartitionValues,
      tableFilter = config.datagen.tableFilter,
      numPartitions = config.datagen.numPartitions
    )
  }

  /**
   * 验证生成的数据
   */
  def validateData(): Unit = {
    println("验证生成的数据...")
    
    val tableNames = if (config.datagen.tableFilter.nonEmpty) {
      config.datagen.tableFilter
    } else {
      Set("catalog_sales", "web_sales", "store_sales", "item", "customer", "date_dim")
    }

    tableNames.foreach { tableName =>
      try {
        val rowCount = if (config.iceberg.enabled) {
          val catalogName = config.iceberg.defaultCatalog
          spark.table(s"$catalogName.tpcds.$tableName").count()
        } else {
          spark.read.parquet(s"${config.datagen.outputLocation}/$tableName").count()
        }
        println(s"✓ 表 $tableName: $rowCount 行")
      } catch {
        case e: Exception =>
          println(s"✗ 验证表 $tableName 失败: ${e.getMessage}")
      }
    }
  }

  /**
   * 清理资源
   */
  def cleanup(): Unit = {
    if (spark != null) {
      spark.stop()
    }
  }
}

/**
 * 命令行入口
 */
object TPCDSDataGenerator {
  
  def main(args: Array[String]): Unit = {
    val arguments = parseArguments(args)
    
    try {
      // 加载配置
      val config = loadConfiguration(arguments)
      
      // 创建数据生成器
      val generator = new TPCDSDataGenerator(config)
      
      // 初始化
      generator.initialize()
      
      // 生成数据
      generator.generateData()
      
      // 验证数据
      generator.validateData()
      
      // 清理
      generator.cleanup()
      
    } catch {
      case e: Exception =>
        println(s"数据生成失败: ${e.getMessage}")
        e.printStackTrace()
        System.exit(1)
    }
  }

  /**
   * 解析命令行参数
   */
  private def parseArguments(args: Array[String]): Map[String, String] = {
    val argMap = scala.collection.mutable.Map[String, String]()
    
    var i = 0
    while (i < args.length) {
      args(i) match {
        case "--config" | "-c" =>
          if (i + 1 < args.length) {
            argMap("config") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--config 需要指定配置文件路径")
          }
        case "--template" | "-t" =>
          if (i + 1 < args.length) {
            argMap("template") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--template 需要指定模板名称")
          }
        case "--output-location" =>
          if (i + 1 < args.length) {
            argMap("output-location") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--output-location 需要指定输出路径")
          }
        case "--scale-factor" =>
          if (i + 1 < args.length) {
            argMap("scale-factor") = args(i + 1)
            i += 2
          } else {
            throw new IllegalArgumentException("--scale-factor 需要指定规模因子")
          }
        case "--iceberg" =>
          argMap("iceberg") = "true"
          i += 1
        case "--help" | "-h" =>
          printUsage()
          System.exit(0)
        case other =>
          throw new IllegalArgumentException(s"未知参数: $other")
      }
    }
    
    argMap.toMap
  }

  /**
   * 加载配置
   */
  private def loadConfiguration(arguments: Map[String, String]): BenchmarkConfig = {
    val configManager = ConfigManager()
    
    // 优先级：命令行参数 > 配置文件 > 模板 > 默认配置
    val baseConfig = arguments.get("template") match {
      case Some(template) => ConfigManager.loadTemplate(template)
      case None => arguments.get("config") match {
        case Some(configPath) => configManager.loadFromFile(configPath).get
        case None => ConfigTemplates.localDev()
      }
    }

    // 应用命令行参数覆盖
    val finalConfig = applyCommandLineOverrides(baseConfig, arguments)
    
    // 验证配置
    validateConfiguration(finalConfig)
    
    finalConfig
  }

  /**
   * 应用命令行参数覆盖
   */
  private def applyCommandLineOverrides(config: BenchmarkConfig, arguments: Map[String, String]): BenchmarkConfig = {
    var updatedConfig = config
    
    arguments.get("output-location").foreach { location =>
      updatedConfig = updatedConfig.copy(
        datagen = updatedConfig.datagen.copy(outputLocation = location)
      )
    }
    
    arguments.get("scale-factor").foreach { factor =>
      updatedConfig = updatedConfig.copy(
        datagen = updatedConfig.datagen.copy(scaleFactor = factor.toInt)
      )
    }
    
    if (arguments.contains("iceberg")) {
      updatedConfig = updatedConfig.copy(
        iceberg = updatedConfig.iceberg.copy(enabled = true)
      )
    }
    
    updatedConfig
  }

  /**
   * 验证配置
   */
  private def validateConfiguration(config: BenchmarkConfig): Unit = {
    if (config.datagen.outputLocation.isEmpty) {
      throw new IllegalArgumentException("必须指定输出位置")
    }
    
    if (config.datagen.scaleFactor <= 0) {
      throw new IllegalArgumentException("规模因子必须大于0")
    }
    
    if (config.iceberg.enabled && config.iceberg.catalogs.isEmpty) {
      throw new IllegalArgumentException("启用Iceberg时必须配置至少一个catalog")
    }
  }

  /**
   * 打印使用说明
   */
  private def printUsage(): Unit = {
    println("""
      |TPC-DS数据生成器 - 支持Iceberg表格式
      |
      |用法: spark-submit --class TPCDSDataGenerator [选项]
      |
      |选项:
      |  -c, --config <文件>          配置文件路径
      |  -t, --template <模板>        使用预定义模板 (local, pb-scale, production-hive)
      |  --output-location <路径>     输出数据位置
      |  --scale-factor <数字>        数据规模因子
      |  --iceberg                   启用Iceberg格式
      |  -h, --help                  显示此帮助信息
      |
      |示例:
      |  # 使用本地开发模板
      |  spark-submit --class TPCDSDataGenerator --template local --output-location /tmp/data
      |
      |  # 使用PB级模板
      |  spark-submit --class TPCDSDataGenerator --template pb-scale --scale-factor 10000
      |
      |  # 使用配置文件
      |  spark-submit --class TPCDSDataGenerator --config benchmark.properties
      |""".stripMargin)
  }
}
