/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.benchmark.config

import scala.collection.mutable

/**
 * 基准测试配置类
 * 支持从配置文件加载各种配置参数，避免硬编码
 */
case class BenchmarkConfig(
    spark: SparkConfig,
    iceberg: IcebergConfig,
    datagen: DatagenConfig,
    benchmark: BenchmarkTestConfig
)

/**
 * Spark相关配置
 */
case class SparkConfig(
    master: String = "local[*]",
    appName: String = "TPC-DS Iceberg Benchmark",
    executor: ExecutorConfig = ExecutorConfig(),
    sql: SqlConfig = SqlConfig(),
    additionalConfigs: Map[String, String] = Map.empty
)

case class ExecutorConfig(
    memory: String = "4g",
    cores: Int = 2,
    instances: Option[Int] = None,
    memoryFraction: Double = 0.8
)

case class SqlConfig(
    adaptive: AdaptiveConfig = AdaptiveConfig(),
    cbo: CboConfig = CboConfig(),
    additionalConfigs: Map[String, String] = Map.empty
)

case class AdaptiveConfig(
    enabled: Boolean = true,
    advisoryPartitionSizeInBytes: String = "128MB",
    coalescePartitions: CoalesceConfig = CoalesceConfig()
)

case class CoalesceConfig(
    enabled: Boolean = true,
    minPartitionNum: Int = 1,
    initialPartitionNum: Option[Int] = None
)

case class CboConfig(
    enabled: Boolean = false,
    planStatsEnabled: Boolean = false,
    joinReorderEnabled: Boolean = false,
    histogramEnabled: Boolean = false
)

/**
 * Iceberg相关配置
 */
case class IcebergConfig(
    enabled: Boolean = false,
    catalogs: Map[String, CatalogConfig] = Map.empty,
    defaultCatalog: String = "hadoop_prod",
    tableProperties: Map[String, String] = Map.empty,
    formatVersion: Int = 2
)

case class CatalogConfig(
    catalogType: String, // "hadoop" or "hive"
    warehouse: String,
    uri: Option[String] = None, // for hive catalog
    properties: Map[String, String] = Map.empty
)

/**
 * 数据生成配置
 */
case class DatagenConfig(
    outputLocation: String,
    scaleFactor: Int = 1,
    format: String = "parquet",
    overwrite: Boolean = false,
    partitionTables: Boolean = false,
    useDoubleForDecimal: Boolean = false,
    useStringForChar: Boolean = false,
    clusterByPartitionColumns: Boolean = false,
    filterOutNullPartitionValues: Boolean = false,
    tableFilter: Set[String] = Set.empty,
    numPartitions: Int = 100
)

/**
 * 基准测试配置
 */
case class BenchmarkTestConfig(
    dataLocation: String,
    queryFilter: Set[String] = Set.empty,
    iterations: Int = 1,
    warmupIterations: Int = 0,
    outputFormat: String = "console", // console, json, csv
    outputLocation: Option[String] = None
)

/**
 * 表配置
 */
case class TableConfig(
    name: String,
    partitionColumns: Seq[String] = Seq.empty,
    properties: Map[String, String] = Map.empty,
    sortOrder: Seq[String] = Seq.empty
)

/**
 * 配置构建器，支持链式调用
 */
class BenchmarkConfigBuilder {
  private var sparkConfig = SparkConfig()
  private var icebergConfig = IcebergConfig()
  private var datagenConfig = DatagenConfig("")
  private var benchmarkConfig = BenchmarkTestConfig("")

  def withSpark(config: SparkConfig): BenchmarkConfigBuilder = {
    this.sparkConfig = config
    this
  }

  def withIceberg(config: IcebergConfig): BenchmarkConfigBuilder = {
    this.icebergConfig = config
    this
  }

  def withDatagen(config: DatagenConfig): BenchmarkConfigBuilder = {
    this.datagenConfig = config
    this
  }

  def withBenchmark(config: BenchmarkTestConfig): BenchmarkConfigBuilder = {
    this.benchmarkConfig = config
    this
  }

  def build(): BenchmarkConfig = {
    BenchmarkConfig(sparkConfig, icebergConfig, datagenConfig, benchmarkConfig)
  }
}

object BenchmarkConfigBuilder {
  def apply(): BenchmarkConfigBuilder = new BenchmarkConfigBuilder()
}

/**
 * 预定义的配置模板
 */
object ConfigTemplates {
  
  /**
   * 本地开发环境配置
   */
  def localDev(): BenchmarkConfig = {
    BenchmarkConfig(
      spark = SparkConfig(
        master = "local[2]",
        executor = ExecutorConfig(memory = "2g", cores = 1)
      ),
      iceberg = IcebergConfig(
        enabled = true,
        catalogs = Map(
          "hadoop_local" -> CatalogConfig(
            catalogType = "hadoop",
            warehouse = "file:///tmp/iceberg_warehouse"
          )
        ),
        defaultCatalog = "hadoop_local"
      ),
      datagen = DatagenConfig(
        outputLocation = "/tmp/tpcds-data",
        scaleFactor = 1,
        numPartitions = 4
      ),
      benchmark = BenchmarkTestConfig(
        dataLocation = "/tmp/tpcds-data"
      )
    )
  }

  /**
   * PB级数据场景配置
   */
  def petabyteScale(): BenchmarkConfig = {
    BenchmarkConfig(
      spark = SparkConfig(
        master = "yarn",
        executor = ExecutorConfig(
          memory = "16g",
          cores = 8,
          instances = Some(100)
        ),
        sql = SqlConfig(
          adaptive = AdaptiveConfig(
            enabled = true,
            advisoryPartitionSizeInBytes = "256MB",
            coalescePartitions = CoalesceConfig(
              enabled = true,
              minPartitionNum = 1,
              initialPartitionNum = Some(1000)
            )
          )
        )
      ),
      iceberg = IcebergConfig(
        enabled = true,
        catalogs = Map(
          "hadoop_prod" -> CatalogConfig(
            catalogType = "hadoop",
            warehouse = "hdfs://namenode:9000/warehouse/iceberg",
            properties = Map(
              "write.format.default" -> "parquet",
              "write.parquet.compression-codec" -> "zstd"
            )
          )
        ),
        defaultCatalog = "hadoop_prod",
        tableProperties = Map(
          "write.target-file-size-bytes" -> "1073741824", // 1GB
          "write.parquet.row-group-size-bytes" -> "134217728", // 128MB
          "write.parquet.page-size-bytes" -> "1048576", // 1MB
          "write.distribution-mode" -> "hash"
        ),
        formatVersion = 2
      ),
      datagen = DatagenConfig(
        outputLocation = "hdfs://namenode:9000/tpcds-data",
        scaleFactor = 10000, // 10TB
        numPartitions = 10000,
        partitionTables = true
      ),
      benchmark = BenchmarkTestConfig(
        dataLocation = "hdfs://namenode:9000/tpcds-data",
        iterations = 3,
        warmupIterations = 1
      )
    )
  }

  /**
   * 生产环境Hive catalog配置
   */
  def productionHive(): BenchmarkConfig = {
    BenchmarkConfig(
      spark = SparkConfig(
        master = "yarn",
        executor = ExecutorConfig(memory = "8g", cores = 4)
      ),
      iceberg = IcebergConfig(
        enabled = true,
        catalogs = Map(
          "hive_prod" -> CatalogConfig(
            catalogType = "hive",
            warehouse = "hdfs://namenode:9000/warehouse/hive",
            uri = Some("thrift://hive-metastore:9083")
          )
        ),
        defaultCatalog = "hive_prod"
      ),
      datagen = DatagenConfig(
        outputLocation = "hdfs://namenode:9000/tpcds-data",
        scaleFactor = 1000
      ),
      benchmark = BenchmarkTestConfig(
        dataLocation = "hdfs://namenode:9000/tpcds-data"
      )
    )
  }
}
