/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.sql.execution.benchmark.sql

import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.execution.benchmark.config.{BenchmarkConfig, TableConfig}
import org.apache.spark.sql.types.StructType

/**
 * 基于Spark SQL的表创建器
 * 使用DDL语句创建和管理Iceberg表，支持灵活的配置
 */
class TableCreator(spark: SparkSession, config: BenchmarkConfig) {

  /**
   * 创建Iceberg表
   */
  def createIcebergTable(
      tableName: String,
      schema: StructType,
      tableConfig: TableConfig,
      location: String): Unit = {
    
    val catalogName = config.iceberg.defaultCatalog
    val fullTableName = s"$catalogName.$tableName"
    
    // 构建DDL语句
    val ddl = buildCreateTableDDL(fullTableName, schema, tableConfig, location)
    
    // 执行DDL
    spark.sql(ddl)
    
    // 如果有额外的表属性，使用ALTER TABLE设置
    setAdditionalTableProperties(fullTableName, tableConfig)
  }

  /**
   * 构建CREATE TABLE DDL语句
   */
  private def buildCreateTableDDL(
      fullTableName: String,
      schema: StructType,
      tableConfig: TableConfig,
      location: String): String = {
    
    val schemaDDL = schema.toDDL
    val partitionClause = buildPartitionClause(tableConfig.partitionColumns)
    val propertiesClause = buildTablePropertiesClause(tableConfig.properties)
    val locationClause = s"LOCATION '$location'"
    
    s"""
       |CREATE TABLE IF NOT EXISTS $fullTableName (
       |  $schemaDDL
       |) USING iceberg
       |$partitionClause
       |$propertiesClause
       |$locationClause
       |""".stripMargin
  }

  /**
   * 构建分区子句
   */
  private def buildPartitionClause(partitionColumns: Seq[String]): String = {
    if (partitionColumns.nonEmpty) {
      s"PARTITIONED BY (${partitionColumns.mkString(", ")})"
    } else {
      ""
    }
  }

  /**
   * 构建表属性子句
   */
  private def buildTablePropertiesClause(properties: Map[String, String]): String = {
    val allProperties = config.iceberg.tableProperties ++ properties
    
    if (allProperties.nonEmpty) {
      val propertiesStr = allProperties.map { case (key, value) =>
        s"'$key' = '$value'"
      }.mkString(", ")
      s"TBLPROPERTIES ($propertiesStr)"
    } else {
      ""
    }
  }

  /**
   * 设置额外的表属性
   */
  private def setAdditionalTableProperties(fullTableName: String, tableConfig: TableConfig): Unit = {
    // 设置排序顺序
    if (tableConfig.sortOrder.nonEmpty) {
      val sortOrderClause = tableConfig.sortOrder.mkString(", ")
      spark.sql(s"ALTER TABLE $fullTableName WRITE ORDERED BY ($sortOrderClause)")
    }
  }

  /**
   * 创建临时视图（用于非Iceberg场景）
   */
  def createTempView(tableName: String, dataFrame: DataFrame): Unit = {
    dataFrame.createOrReplaceTempView(tableName)
  }

  /**
   * 创建外部表（用于Parquet等格式）
   */
  def createExternalTable(
      tableName: String,
      location: String,
      format: String = "parquet"): Unit = {
    
    spark.sql(s"DROP TABLE IF EXISTS $tableName")
    spark.catalog.createTable(tableName, location, format)
    
    // 尝试恢复分区
    try {
      spark.sql(s"ALTER TABLE $tableName RECOVER PARTITIONS")
    } catch {
      case _: Exception => 
        // 忽略分区恢复失败，可能是非分区表
    }
  }

  /**
   * 优化Iceberg表
   */
  def optimizeIcebergTable(tableName: String): Unit = {
    val catalogName = config.iceberg.defaultCatalog
    val fullTableName = s"$catalogName.$tableName"
    
    // 执行文件压缩
    spark.sql(s"CALL $catalogName.system.rewrite_data_files('$fullTableName')")
    
    // 执行清理过期文件
    spark.sql(s"CALL $catalogName.system.expire_snapshots('$fullTableName')")
    
    // 执行清理孤儿文件
    spark.sql(s"CALL $catalogName.system.remove_orphan_files('$fullTableName')")
  }

  /**
   * 分析表统计信息
   */
  def analyzeTable(tableName: String, analyzeColumns: Boolean = true): Unit = {
    val catalogName = if (config.iceberg.enabled) config.iceberg.defaultCatalog else ""
    val fullTableName = if (catalogName.nonEmpty) s"$catalogName.$tableName" else tableName
    
    if (analyzeColumns) {
      spark.sql(s"ANALYZE TABLE $fullTableName COMPUTE STATISTICS FOR ALL COLUMNS")
    } else {
      spark.sql(s"ANALYZE TABLE $fullTableName COMPUTE STATISTICS")
    }
  }

  /**
   * 获取表信息
   */
  def getTableInfo(tableName: String): DataFrame = {
    val catalogName = if (config.iceberg.enabled) config.iceberg.defaultCatalog else ""
    val fullTableName = if (catalogName.nonEmpty) s"$catalogName.$tableName" else tableName
    
    spark.sql(s"DESCRIBE EXTENDED $fullTableName")
  }

  /**
   * 获取表的行数
   */
  def getTableRowCount(tableName: String): Long = {
    val catalogName = if (config.iceberg.enabled) config.iceberg.defaultCatalog else ""
    val fullTableName = if (catalogName.nonEmpty) s"$catalogName.$tableName" else tableName
    
    spark.table(fullTableName).count()
  }

  /**
   * 删除表
   */
  def dropTable(tableName: String): Unit = {
    val catalogName = if (config.iceberg.enabled) config.iceberg.defaultCatalog else ""
    val fullTableName = if (catalogName.nonEmpty) s"$catalogName.$tableName" else tableName
    
    spark.sql(s"DROP TABLE IF EXISTS $fullTableName")
  }
}

/**
 * 表配置工厂
 */
object TableConfigFactory {
  
  /**
   * 创建TPC-DS表配置
   */
  def createTpcdsTableConfigs(config: BenchmarkConfig): Map[String, TableConfig] = {
    val baseProperties = Map(
      "format-version" -> config.iceberg.formatVersion.toString
    ) ++ config.iceberg.tableProperties

    Map(
      "catalog_sales" -> TableConfig(
        name = "catalog_sales",
        partitionColumns = Seq("cs_sold_date_sk"),
        properties = baseProperties ++ Map(
          "write.target-file-size-bytes" -> "268435456" // 256MB for large fact table
        ),
        sortOrder = Seq("cs_sold_date_sk", "cs_item_sk")
      ),
      "web_sales" -> TableConfig(
        name = "web_sales",
        partitionColumns = Seq("ws_sold_date_sk"),
        properties = baseProperties ++ Map(
          "write.target-file-size-bytes" -> "268435456"
        ),
        sortOrder = Seq("ws_sold_date_sk", "ws_item_sk")
      ),
      "store_sales" -> TableConfig(
        name = "store_sales",
        partitionColumns = Seq("ss_sold_date_sk"),
        properties = baseProperties ++ Map(
          "write.target-file-size-bytes" -> "268435456"
        ),
        sortOrder = Seq("ss_sold_date_sk", "ss_item_sk")
      ),
      "inventory" -> TableConfig(
        name = "inventory",
        partitionColumns = Seq("inv_date_sk"),
        properties = baseProperties,
        sortOrder = Seq("inv_date_sk", "inv_item_sk")
      ),
      // 维度表通常不分区
      "item" -> TableConfig(
        name = "item",
        partitionColumns = Seq.empty,
        properties = baseProperties ++ Map(
          "write.target-file-size-bytes" -> "67108864" // 64MB for dimension table
        ),
        sortOrder = Seq("i_item_sk")
      ),
      "customer" -> TableConfig(
        name = "customer",
        partitionColumns = Seq.empty,
        properties = baseProperties ++ Map(
          "write.target-file-size-bytes" -> "67108864"
        ),
        sortOrder = Seq("c_customer_sk")
      ),
      "date_dim" -> TableConfig(
        name = "date_dim",
        partitionColumns = Seq.empty,
        properties = baseProperties ++ Map(
          "write.target-file-size-bytes" -> "33554432" // 32MB for small dimension table
        ),
        sortOrder = Seq("d_date_sk")
      )
      // 可以继续添加其他表的配置...
    )
  }

  /**
   * 为PB级场景创建优化的表配置
   */
  def createPetabyteScaleConfigs(config: BenchmarkConfig): Map[String, TableConfig] = {
    val baseConfigs = createTpcdsTableConfigs(config)
    
    // 为PB级场景优化配置
    baseConfigs.map { case (tableName, tableConfig) =>
      val optimizedProperties = tableConfig.properties ++ Map(
        "write.target-file-size-bytes" -> "1073741824", // 1GB
        "write.parquet.row-group-size-bytes" -> "134217728", // 128MB
        "write.parquet.page-size-bytes" -> "1048576", // 1MB
        "write.distribution-mode" -> "hash",
        "write.delete.mode" -> "merge-on-read"
      )
      
      tableName -> tableConfig.copy(properties = optimizedProperties)
    }
  }
}
