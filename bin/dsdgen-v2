#!/usr/bin/env bash

#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# 新版TPC-DS数据生成器启动脚本
# 支持配置文件和模板系统
#

if [ -z "${SPARK_HOME}" ]; then
  echo "错误: 环境变量 SPARK_HOME 未设置" 1>&2
  echo "请设置 SPARK_HOME 指向您的 Spark 安装目录" 1>&2
  exit 1
fi

# 确定当前工作目录
_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 解析命令行参数
TEMPLATE=""
CONFIG_FILE=""
SPARK_CONF=""
ADDITIONAL_ARGS=""

while [[ $# -gt 0 ]]; do
  case $1 in
    -t|--template)
      TEMPLATE="$2"
      shift 2
      ;;
    -c|--config)
      CONFIG_FILE="$2"
      shift 2
      ;;
    --spark-conf)
      SPARK_CONF="$SPARK_CONF --conf $2"
      shift 2
      ;;
    --help|-h)
      echo "TPC-DS数据生成器 - 新版本"
      echo ""
      echo "用法: $0 [选项]"
      echo ""
      echo "选项:"
      echo "  -t, --template <模板>        使用预定义模板 (local, pb-scale, hive)"
      echo "  -c, --config <文件>          使用配置文件"
      echo "  --spark-conf <配置>          额外的Spark配置"
      echo "  --output-location <路径>     输出数据位置"
      echo "  --scale-factor <数字>        数据规模因子"
      echo "  --iceberg                   启用Iceberg格式"
      echo "  -h, --help                  显示此帮助信息"
      echo ""
      echo "预定义模板:"
      echo "  local      - 本地开发环境 (默认)"
      echo "  pb-scale   - PB级数据场景"
      echo "  hive       - 生产环境(Hive Catalog)"
      echo ""
      echo "示例:"
      echo "  # 使用本地模板"
      echo "  $0 --template local --output-location /tmp/data"
      echo ""
      echo "  # 使用PB级模板"
      echo "  $0 --template pb-scale --scale-factor 10000"
      echo ""
      echo "  # 使用自定义配置文件"
      echo "  $0 --config /path/to/config.properties"
      echo ""
      exit 0
      ;;
    *)
      ADDITIONAL_ARGS="$ADDITIONAL_ARGS $1"
      shift
      ;;
  esac
done

# 设置默认模板
if [ -z "$TEMPLATE" ] && [ -z "$CONFIG_FILE" ]; then
  TEMPLATE="local"
  echo "使用默认模板: local" 1>&2
fi

# 查找资源文件
find_resource() {
  local tpcds_datagen_version=`grep "<version>" "${_DIR}/../pom.xml" | head -n2 | tail -n1 | awk -F '[<>]' '{print $3}'`
  local jar_file="iceberg-spark-tpcds-benchmark-${tpcds_datagen_version}-with-dependencies.jar"
  local built_jar="$_DIR/../target/${jar_file}"
  if [ -e "$built_jar" ]; then
    RESOURCE=$built_jar
  else
    RESOURCE="$_DIR/../assembly/${jar_file}"
    echo "未找到 ${built_jar}，使用预编译的 ${RESOURCE}" 1>&2
  fi
}

# 根据模板设置Spark配置
setup_spark_config_for_template() {
  case "$TEMPLATE" in
    "local")
      SPARK_CONF="$SPARK_CONF --master local[2]"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.memory=2g"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.cores=1"
      ;;
    "pb-scale")
      SPARK_CONF="$SPARK_CONF --master yarn"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.memory=16g"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.cores=8"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.instances=100"
      SPARK_CONF="$SPARK_CONF --conf spark.dynamicAllocation.enabled=true"
      SPARK_CONF="$SPARK_CONF --conf spark.dynamicAllocation.minExecutors=50"
      SPARK_CONF="$SPARK_CONF --conf spark.dynamicAllocation.maxExecutors=200"
      ;;
    "hive")
      SPARK_CONF="$SPARK_CONF --master yarn"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.memory=8g"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.cores=4"
      SPARK_CONF="$SPARK_CONF --conf spark.executor.instances=50"
      ;;
  esac
}

# 设置Iceberg相关的Spark配置
setup_iceberg_config() {
  SPARK_CONF="$SPARK_CONF --conf spark.sql.extensions=org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions"
  
  case "$TEMPLATE" in
    "local")
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hadoop_local=org.apache.iceberg.spark.SparkCatalog"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hadoop_local.type=hadoop"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hadoop_local.warehouse=file:///tmp/iceberg_warehouse"
      ;;
    "pb-scale")
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hadoop_pb=org.apache.iceberg.spark.SparkCatalog"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hadoop_pb.type=hadoop"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hadoop_pb.warehouse=hdfs://namenode:9000/warehouse/iceberg_pb"
      ;;
    "hive")
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hive_prod=org.apache.iceberg.spark.SparkCatalog"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hive_prod.type=hive"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hive_prod.warehouse=hdfs://namenode:9000/warehouse/hive"
      SPARK_CONF="$SPARK_CONF --conf spark.sql.catalog.hive_prod.uri=thrift://hive-metastore:9083"
      ;;
  esac
}

# 检查是否需要Iceberg配置
check_iceberg_needed() {
  if echo "$ADDITIONAL_ARGS" | grep -q "\--iceberg" || [ "$TEMPLATE" != "" ]; then
    return 0
  else
    return 1
  fi
}

find_resource

# 设置模板相关配置
if [ -n "$TEMPLATE" ]; then
  setup_spark_config_for_template
  if check_iceberg_needed; then
    setup_iceberg_config
  fi
fi

# 设置必要的JAR文件
# 尝试查找可用的Iceberg JAR文件
ICEBERG_JARS=""
for jar in "$_DIR/../assembly/iceberg-spark-runtime-"*.jar; do
  if [ -f "$jar" ]; then
    ICEBERG_JARS="$jar"
    break
  fi
done

if [ -n "$ICEBERG_JARS" ]; then
  SPARK_CONF="$SPARK_CONF --jars $ICEBERG_JARS"
  echo "使用Iceberg JAR: $ICEBERG_JARS" 1>&2
else
  echo "警告: 未找到Iceberg JAR文件在 $_DIR/../assembly/" 1>&2
  echo "请确保已下载Iceberg依赖" 1>&2
fi

echo "使用 spark-submit 路径: $SPARK_HOME" 1>&2
echo "Spark配置: $SPARK_CONF" 1>&2

# 构建最终的参数列表
FINAL_ARGS=""
if [ -n "$TEMPLATE" ]; then
  FINAL_ARGS="$FINAL_ARGS --template $TEMPLATE"
fi
if [ -n "$CONFIG_FILE" ]; then
  FINAL_ARGS="$FINAL_ARGS --config $CONFIG_FILE"
fi
FINAL_ARGS="$FINAL_ARGS $ADDITIONAL_ARGS"

echo "执行命令参数: $FINAL_ARGS" 1>&2

exec "${SPARK_HOME}"/bin/spark-submit \
  --class org.apache.spark.sql.execution.benchmark.TPCDSDataGenerator \
  $SPARK_CONF \
  ${RESOURCE} \
  $FINAL_ARGS
