# Iceberg Spark TPC-DS 基准测试项目改造计划

## 项目现状分析

### 当前架构优点
- 基于TPC-DS标准基准测试
- 支持Iceberg表格式
- 包含完整的数据生成和查询测试功能
- 代码结构清晰，模块化程度较好

### 主要局限性
1. **配置硬编码严重**
   - Spark配置（master、内存等）硬编码在代码中
   - Iceberg catalog配置硬编码
   - 表结构定义硬编码
   - 性能优化参数硬编码

2. **缺乏灵活性**
   - 无法通过配置文件管理不同环境
   - 无法动态调整Iceberg表格式参数
   - 缺乏对PB级数据场景的专门优化

3. **可扩展性不足**
   - 只支持基本的Hadoop和Hive catalog
   - 缺乏对不同存储后端的支持
   - 无法配置复杂的性能优化参数

## 改造目标

### 核心目标
1. **配置外部化**：将所有硬编码配置移至配置文件
2. **基于Spark SQL**：使用Spark SQL进行表创建和管理
3. **支持PB级场景**：针对PB级数据进行专门优化
4. **灵活的Iceberg配置**：支持可配置的Iceberg表格式参数
5. **中文文档**：提供完整的中文使用文档

### 技术改进
1. **配置管理**
   - 使用YAML/Properties配置文件
   - 支持环境特定配置
   - 支持配置模板和继承

2. **Spark SQL集成**
   - 使用DDL语句创建Iceberg表
   - 支持动态SQL配置
   - 集成Spark Catalog API

3. **性能优化**
   - 可配置的分区策略
   - 可配置的文件大小和压缩
   - 支持列式存储优化
   - 内存和并行度优化

## 改造实施计划

### 第一阶段：配置外部化
1. 创建配置文件结构
2. 重构配置加载机制
3. 移除硬编码配置

### 第二阶段：Spark SQL集成
1. 重构表创建逻辑
2. 实现基于SQL的表管理
3. 集成Catalog API

### 第三阶段：Iceberg优化
1. 实现可配置的表格式参数
2. 添加PB级数据优化
3. 支持多种catalog类型

### 第四阶段：文档和测试
1. 编写中文文档
2. 添加配置示例
3. 完善测试用例

## 文件结构规划

```
src/main/
├── scala/org/apache/spark/sql/execution/benchmark/
│   ├── config/
│   │   ├── ConfigManager.scala          # 配置管理器
│   │   ├── BenchmarkConfig.scala        # 基准测试配置
│   │   └── IcebergConfig.scala          # Iceberg配置
│   ├── sql/
│   │   ├── TableCreator.scala           # 基于SQL的表创建
│   │   └── CatalogManager.scala         # Catalog管理
│   ├── TPCDSDataGenerator.scala         # 重构后的数据生成器
│   └── TPCDSBenchmark.scala             # 重构后的基准测试
├── resources/
│   ├── config/
│   │   ├── benchmark-default.yaml       # 默认配置
│   │   ├── benchmark-pb-scale.yaml      # PB级配置
│   │   └── iceberg-catalogs.yaml        # Iceberg catalog配置
│   └── sql/
│       ├── create-tables.sql            # 表创建SQL
│       └── optimize-tables.sql          # 表优化SQL
└── docs/
    ├── README-zh.md                     # 中文文档
    ├── 配置指南.md                       # 配置指南
    └── PB级性能调优.md                   # 性能调优指南
```

## 关键改进点

### 1. 配置文件驱动
```yaml
# benchmark-config.yaml
spark:
  master: "local[*]"
  executor:
    memory: "4g"
    cores: 2
  sql:
    adaptive:
      enabled: true
      coalescePartitions:
        enabled: true

iceberg:
  catalogs:
    hadoop_prod:
      type: "hadoop"
      warehouse: "hdfs://namenode:9000/warehouse/iceberg"
      properties:
        write.format.default: "parquet"
        write.parquet.compression-codec: "zstd"
    hive_prod:
      type: "hive"
      uri: "thrift://hive-metastore:9083"
      warehouse: "hdfs://namenode:9000/warehouse/hive"

tables:
  format_version: 2
  partition_spec:
    catalog_sales: ["cs_sold_date_sk"]
  table_properties:
    write.target-file-size-bytes: "134217728"  # 128MB
    write.delete.mode: "merge-on-read"
```

### 2. 基于Spark SQL的表管理
```scala
// 使用SQL DDL创建Iceberg表
val createTableSQL = s"""
  CREATE TABLE IF NOT EXISTS ${catalogName}.${tableName} (
    ${schema.toDDL}
  ) USING iceberg
  PARTITIONED BY (${partitionColumns.mkString(", ")})
  TBLPROPERTIES (
    'format-version' = '2',
    'write.target-file-size-bytes' = '134217728'
  )
  LOCATION '${tableLocation}'
"""
```

### 3. PB级数据优化配置
```yaml
# pb-scale-config.yaml
spark:
  executor:
    memory: "16g"
    cores: 8
  sql:
    adaptive:
      enabled: true
      advisoryPartitionSizeInBytes: "256MB"
      coalescePartitions:
        minPartitionNum: 1
        initialPartitionNum: 1000

iceberg:
  table_properties:
    write.target-file-size-bytes: "1073741824"  # 1GB for PB scale
    write.parquet.row-group-size-bytes: "134217728"  # 128MB
    write.parquet.page-size-bytes: "1048576"  # 1MB
    write.distribution-mode: "hash"
```

这个改造计划将显著提升项目的灵活性和可扩展性，特别适合PB级数据场景的性能测试需求。
