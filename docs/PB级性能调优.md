# PB级数据场景性能调优指南

## 概述

本指南专门针对PB级数据场景，提供Iceberg Spark TPC-DS基准测试的性能调优建议。PB级数据处理需要特殊的配置和优化策略。

## 硬件资源规划

### 集群规模建议

**最小配置（10TB-100TB）**
- 节点数量：50-100个
- 每节点CPU：16-32核心
- 每节点内存：64-128GB
- 每节点存储：2-4TB SSD

**推荐配置（100TB-1PB）**
- 节点数量：100-500个
- 每节点CPU：32-64核心
- 每节点内存：128-256GB
- 每节点存储：4-8TB NVMe SSD

**大规模配置（1PB+）**
- 节点数量：500-1000个
- 每节点CPU：64-128核心
- 每节点内存：256-512GB
- 每节点存储：8-16TB NVMe SSD

### 网络要求

- 节点间带宽：至少10Gbps，推荐25Gbps+
- 存储网络：专用存储网络，40Gbps+
- 网络延迟：< 1ms（数据中心内）

## Spark配置优化

### 资源分配策略

```properties
# 大规模集群配置
spark.master=yarn
spark.submit.deployMode=cluster

# Executor配置 - 大内存策略
spark.executor.memory=32g
spark.executor.cores=8
spark.executor.instances=200

# 动态资源分配
spark.dynamicAllocation.enabled=true
spark.dynamicAllocation.minExecutors=100
spark.dynamicAllocation.maxExecutors=500
spark.dynamicAllocation.initialExecutors=200
spark.dynamicAllocation.executorIdleTimeout=300s
spark.dynamicAllocation.cachedExecutorIdleTimeout=3600s

# Driver配置
spark.driver.memory=16g
spark.driver.maxResultSize=8g
spark.driver.cores=4
```

### 内存管理优化

```properties
# 内存分配优化
spark.executor.memoryFraction=0.8
spark.storage.memoryFraction=0.3
spark.shuffle.memoryFraction=0.2

# 堆外内存
spark.executor.memoryOffHeap.enabled=true
spark.executor.memoryOffHeap.size=8g

# GC优化
spark.executor.extraJavaOptions=-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseZGC
```

### 序列化和网络优化

```properties
# 序列化优化
spark.serializer=org.apache.spark.serializer.KryoSerializer
spark.kryoserializer.buffer.max=2047m
spark.rdd.compress=true

# 网络优化
spark.network.timeout=800s
spark.sql.broadcastTimeout=36000
spark.rpc.askTimeout=600s
spark.rpc.lookupTimeout=120s

# Shuffle优化
spark.sql.shuffle.partitions=4000
spark.sql.adaptive.shuffle.targetPostShuffleInputSize=256MB
```

## SQL执行优化

### 自适应查询执行（AQE）

```properties
# 启用AQE
spark.sql.adaptive.enabled=true
spark.sql.adaptive.logLevel=INFO

# 分区合并优化
spark.sql.adaptive.coalescePartitions.enabled=true
spark.sql.adaptive.coalescePartitions.minPartitionNum=1
spark.sql.adaptive.coalescePartitions.initialPartitionNum=4000
spark.sql.adaptive.advisoryPartitionSizeInBytes=256MB

# 倾斜连接处理
spark.sql.adaptive.skewJoin.enabled=true
spark.sql.adaptive.skewJoin.skewedPartitionFactor=5
spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes=256MB

# 本地Shuffle读取
spark.sql.adaptive.localShuffleReader.enabled=true
```

### 基于成本的优化（CBO）

```properties
# 启用CBO
spark.sql.cbo.enabled=true
spark.sql.cbo.planStats.enabled=true
spark.sql.cbo.joinReorder.enabled=true
spark.sql.cbo.joinReorder.dp.threshold=12
spark.sql.cbo.joinReorder.card.weight=0.7
spark.sql.cbo.joinReorder.dp.star.filter=true

# 统计信息
spark.sql.statistics.histogram.enabled=true
spark.sql.statistics.histogram.numBins=254
spark.sql.statistics.size.autoUpdate.enabled=true
```

## Iceberg表优化

### 表格式配置

```properties
# 使用Iceberg v2格式
iceberg.format.version=2

# 文件大小优化 - PB级场景
iceberg.table.property.write.target-file-size-bytes=1073741824
iceberg.table.property.write.parquet.row-group-size-bytes=134217728
iceberg.table.property.write.parquet.page-size-bytes=1048576

# 压缩优化
iceberg.table.property.write.parquet.compression-codec=zstd
iceberg.table.property.write.parquet.compression-level=3
iceberg.table.property.write.parquet.dict-size-bytes=2097152
```

### 分区和排序策略

```properties
# 分布模式
iceberg.table.property.write.distribution-mode=hash

# 删除模式优化
iceberg.table.property.write.delete.mode=merge-on-read
iceberg.table.property.write.update.mode=merge-on-read
iceberg.table.property.write.merge.mode=merge-on-read

# 元数据优化
iceberg.table.property.commit.retry.num-retries=10
iceberg.table.property.commit.retry.min-wait-ms=100
iceberg.table.property.commit.retry.max-wait-ms=60000
```

### 高级优化特性

```properties
# Bloom Filter（适用于高选择性查询）
iceberg.table.property.write.parquet.bloom-filter-enabled.column1=true
iceberg.table.property.write.parquet.bloom-filter-max-bytes=1048576

# Z-Order排序（适用于多维查询）
iceberg.table.property.write.sort-order=zorder(col1,col2,col3)

# 文件合并策略
iceberg.table.property.write.target-file-size-bytes=1073741824
iceberg.table.property.write.delete.target-file-size-bytes=67108864
```

## 数据生成优化

### 大规模数据生成配置

```properties
# PB级数据生成
datagen.scale.factor=100000
datagen.num.partitions=50000
datagen.partition.tables=true

# 并行优化
datagen.cluster.by.partition.columns=true
datagen.filter.out.null.partition.values=true

# 输出优化
datagen.overwrite=true
datagen.use.double.for.decimal=false
datagen.use.string.for.char=false
```

### 分区策略

**事实表分区策略**
```sql
-- 按日期分区，适合时间序列查询
PARTITIONED BY (date_sk)

-- 按日期+地区分区，适合地理分析
PARTITIONED BY (date_sk, region_id)

-- 按年月分区，减少分区数量
PARTITIONED BY (year, month)
```

**维度表策略**
```sql
-- 小维度表不分区
-- 大维度表按类别分区
PARTITIONED BY (category_id)
```

## 查询优化策略

### 分区剪枝优化

```sql
-- 启用分区剪枝
SET spark.sql.optimizer.dynamicPartitionPruning.enabled=true;
SET spark.sql.optimizer.dynamicPartitionPruning.useStats=true;
SET spark.sql.optimizer.dynamicPartitionPruning.fallbackFilterRatio=0.5;
```

### 连接优化

```sql
-- 广播连接阈值
SET spark.sql.autoBroadcastJoinThreshold=200MB;

-- 排序合并连接
SET spark.sql.join.preferSortMergeJoin=true;

-- Bucket连接
SET spark.sql.sources.bucketing.enabled=true;
SET spark.sql.sources.bucketing.autoBucketedScan.enabled=true;
```

### 聚合优化

```sql
-- 部分聚合
SET spark.sql.execution.arrow.pyspark.enabled=true;

-- 向量化执行
SET spark.sql.execution.arrow.maxRecordsPerBatch=10000;

-- 列式存储优化
SET spark.sql.columnVector.offheap.enabled=true;
```

## 存储优化

### HDFS配置优化

```xml
<!-- hdfs-site.xml -->
<configuration>
  <!-- 块大小优化 -->
  <property>
    <name>dfs.blocksize</name>
    <value>268435456</value> <!-- 256MB -->
  </property>
  
  <!-- 副本数量 -->
  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>
  
  <!-- 短路读取 -->
  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>true</value>
  </property>
</configuration>
```

### 对象存储优化（S3/OSS）

```properties
# S3优化配置
spark.hadoop.fs.s3a.connection.maximum=200
spark.hadoop.fs.s3a.threads.max=64
spark.hadoop.fs.s3a.connection.timeout=200000
spark.hadoop.fs.s3a.attempts.maximum=20

# 多部分上传
spark.hadoop.fs.s3a.multipart.size=104857600
spark.hadoop.fs.s3a.multipart.threshold=2147483647

# 缓存优化
spark.hadoop.fs.s3a.block.size=134217728
spark.hadoop.fs.s3a.buffer.dir=/tmp/s3a
```

## 监控和调试

### 关键性能指标

1. **吞吐量指标**
   - 数据扫描速度（GB/s）
   - 查询完成时间
   - 并发查询数量

2. **资源利用率**
   - CPU使用率
   - 内存使用率
   - 网络带宽使用率
   - 磁盘I/O使用率

3. **Iceberg特定指标**
   - 文件数量和大小分布
   - 快照数量
   - 元数据操作延迟

### 监控配置

```properties
# 启用详细监控
spark.eventLog.enabled=true
spark.eventLog.dir=hdfs://namenode:9000/spark-events
spark.history.fs.logDirectory=hdfs://namenode:9000/spark-events

# 指标收集
spark.metrics.conf.*.sink.graphite.class=org.apache.spark.metrics.sink.GraphiteSink
spark.metrics.conf.*.sink.graphite.host=graphite-server
spark.metrics.conf.*.sink.graphite.port=2003
```

## 故障排除

### 常见性能问题

1. **数据倾斜**
   - 症状：某些任务执行时间过长
   - 解决：启用AQE倾斜处理，调整分区策略

2. **小文件问题**
   - 症状：文件数量过多，查询性能下降
   - 解决：增加目标文件大小，定期执行文件合并

3. **内存不足**
   - 症状：OOM错误，任务失败
   - 解决：增加executor内存，启用堆外内存

4. **网络瓶颈**
   - 症状：Shuffle阶段缓慢
   - 解决：优化网络配置，减少数据传输

### 性能调优检查清单

- [ ] 集群资源充足（CPU、内存、网络、存储）
- [ ] Spark配置针对PB级场景优化
- [ ] 启用AQE和CBO优化
- [ ] Iceberg表格式和属性配置正确
- [ ] 分区策略符合查询模式
- [ ] 文件大小在合理范围（512MB-2GB）
- [ ] 压缩算法选择合适
- [ ] 监控和告警配置完整

## 性能基准

### 预期性能指标

**数据生成性能**
- 10TB数据：2-4小时
- 100TB数据：1-2天
- 1PB数据：1-2周

**查询性能**
- 简单聚合查询：< 30秒
- 复杂连接查询：< 5分钟
- 全表扫描：< 30分钟

**并发性能**
- 支持100+并发查询
- 平均响应时间 < 2分钟
- 95%查询在5分钟内完成

这些指标会根据具体的硬件配置、数据特征和查询复杂度有所变化。
