# TPC-DS Iceberg 基准测试配置指南

## 概述

本指南详细介绍如何配置TPC-DS Iceberg基准测试工具，包括各种环境下的最佳实践和性能优化建议。

## 配置文件结构

配置文件采用Properties格式，主要包含以下几个部分：

```
# Spark配置
spark.*

# Iceberg配置  
iceberg.*

# 数据生成配置
datagen.*

# 基准测试配置
benchmark.*
```

## Spark配置详解

### 基础配置

```properties
# 集群模式 (local[*], yarn, kubernetes)
spark.master=yarn

# 应用名称
spark.app.name=TPC-DS Iceberg Benchmark

# Executor配置
spark.executor.memory=8g
spark.executor.cores=4
spark.executor.instances=50
spark.executor.memoryFraction=0.8
```

### 内存优化

```properties
# Driver内存配置
spark.driver.memory=4g
spark.driver.maxResultSize=2g

# 序列化器优化
spark.serializer=org.apache.spark.serializer.KryoSerializer

# 内存管理
spark.sql.execution.arrow.maxRecordsPerBatch=10000
```

### SQL优化配置

```properties
# 自适应查询执行
spark.sql.adaptive.enabled=true
spark.sql.adaptive.advisoryPartitionSizeInBytes=128MB
spark.sql.adaptive.coalescePartitions.enabled=true
spark.sql.adaptive.coalescePartitions.minPartitionNum=1

# 基于成本的优化
spark.sql.cbo.enabled=true
spark.sql.cbo.planStats.enabled=true
spark.sql.cbo.joinReorder.enabled=true
spark.sql.statistics.histogram.enabled=true

# 倾斜处理
spark.sql.adaptive.skewJoin.enabled=true
spark.sql.adaptive.localShuffleReader.enabled=true
```

## Iceberg配置详解

### 基础配置

```properties
# 启用Iceberg
iceberg.enabled=true

# 默认catalog
iceberg.default.catalog=hadoop_prod

# 格式版本 (1 或 2，推荐使用2)
iceberg.format.version=2
```

### Hadoop Catalog配置

适用于不依赖Hive Metastore的场景：

```properties
# Catalog基础配置
iceberg.catalog.hadoop_prod.type=hadoop
iceberg.catalog.hadoop_prod.warehouse=hdfs://namenode:9000/warehouse/iceberg

# 写入格式配置
iceberg.catalog.hadoop_prod.property.write.format.default=parquet
iceberg.catalog.hadoop_prod.property.write.parquet.compression-codec=zstd

# 文件大小配置
iceberg.catalog.hadoop_prod.property.write.target-file-size-bytes=268435456
```

### Hive Catalog配置

适用于已有Hive Metastore的环境：

```properties
# Catalog基础配置
iceberg.catalog.hive_prod.type=hive
iceberg.catalog.hive_prod.warehouse=hdfs://namenode:9000/warehouse/hive
iceberg.catalog.hive_prod.uri=thrift://hive-metastore:9083

# 兼容性配置
iceberg.catalog.hive_prod.property.hive.metastore.sasl.enabled=false
```

### 表属性配置

```properties
# 文件大小优化
iceberg.table.property.write.target-file-size-bytes=268435456
iceberg.table.property.write.parquet.row-group-size-bytes=67108864
iceberg.table.property.write.parquet.page-size-bytes=1048576

# 压缩配置
iceberg.table.property.write.parquet.compression-codec=zstd
iceberg.table.property.write.parquet.dict-size-bytes=2097152

# 分布模式
iceberg.table.property.write.distribution-mode=hash

# 删除和更新模式
iceberg.table.property.write.delete.mode=merge-on-read
iceberg.table.property.write.update.mode=merge-on-read
```

## 数据生成配置

### 基础配置

```properties
# 输出位置
datagen.output.location=hdfs://namenode:9000/tpcds-data

# 数据规模因子
datagen.scale.factor=1000

# 输出格式
datagen.format=parquet

# 是否覆盖现有数据
datagen.overwrite=true
```

### 分区和优化配置

```properties
# 是否创建分区表
datagen.partition.tables=true

# 分区数量
datagen.num.partitions=1000

# 数据类型优化
datagen.use.double.for.decimal=false
datagen.use.string.for.char=false

# 分区优化
datagen.cluster.by.partition.columns=true
datagen.filter.out.null.partition.values=true

# 表过滤 (可选，只生成指定表)
datagen.table.filter=catalog_sales,store_sales,web_sales
```

## 不同场景的配置建议

### 本地开发环境

```properties
# 最小资源配置
spark.master=local[2]
spark.executor.memory=2g
spark.executor.cores=1

# 小规模数据
datagen.scale.factor=1
datagen.num.partitions=4
datagen.output.location=/tmp/tpcds-data

# 本地文件系统
iceberg.catalog.hadoop_local.warehouse=file:///tmp/iceberg_warehouse
```

### 中等规模生产环境

```properties
# 集群配置
spark.master=yarn
spark.executor.memory=8g
spark.executor.cores=4
spark.executor.instances=50

# 中等规模数据
datagen.scale.factor=1000
datagen.num.partitions=1000

# HDFS存储
iceberg.catalog.hadoop_prod.warehouse=hdfs://namenode:9000/warehouse/iceberg
```

### PB级大规模环境

```properties
# 大规模集群配置
spark.master=yarn
spark.executor.memory=16g
spark.executor.cores=8
spark.executor.instances=100

# 动态资源分配
spark.dynamicAllocation.enabled=true
spark.dynamicAllocation.minExecutors=50
spark.dynamicAllocation.maxExecutors=200

# 大规模数据
datagen.scale.factor=10000
datagen.num.partitions=10000

# 大文件优化
iceberg.table.property.write.target-file-size-bytes=1073741824
iceberg.table.property.write.parquet.row-group-size-bytes=134217728
```

## 性能调优建议

### 内存调优

1. **Executor内存**：根据数据大小调整，通常8-16GB
2. **内存分配比例**：保持默认的0.8，避免GC压力
3. **Driver内存**：对于大规模作业，建议4-8GB

### 并行度调优

1. **分区数量**：通常为CPU核心数的2-3倍
2. **文件大小**：目标128MB-1GB，根据查询模式调整
3. **Executor数量**：根据集群资源和数据大小确定

### 存储调优

1. **压缩算法**：推荐ZSTD，平衡压缩率和性能
2. **文件格式**：使用Parquet，启用字典编码
3. **分区策略**：基于查询模式选择分区列

### 查询优化

1. **启用CBO**：对于复杂查询场景
2. **自适应执行**：启用AQE处理数据倾斜
3. **统计信息**：定期更新表统计信息

## 监控和调试

### 启用详细日志

```properties
# Spark事件日志
spark.eventLog.enabled=true
spark.eventLog.dir=hdfs://namenode:9000/spark-events

# SQL执行计划
spark.sql.adaptive.logLevel=INFO

# Iceberg日志
log4j.logger.org.apache.iceberg=DEBUG
```

### 性能指标收集

```properties
# 启用指标收集
spark.sql.execution.arrow.pyspark.enabled=true
spark.serializer=org.apache.spark.serializer.KryoSerializer

# 网络超时配置
spark.network.timeout=800s
spark.sql.broadcastTimeout=36000
```

## 故障排除

### 常见问题和解决方案

1. **内存不足错误**
   - 增加executor内存
   - 减少并行度
   - 启用动态资源分配

2. **Catalog连接失败**
   - 检查网络连通性
   - 验证认证配置
   - 确认服务状态

3. **文件过多问题**
   - 增加目标文件大小
   - 启用文件合并
   - 定期执行表优化

4. **查询性能问题**
   - 启用CBO优化
   - 更新表统计信息
   - 检查分区剪枝

### 配置验证

使用以下命令验证配置：

```bash
# 验证Spark配置
spark-submit --class org.apache.spark.sql.execution.benchmark.ConfigValidator

# 验证Iceberg连接
./bin/dsdgen-v2 --template local --help

# 检查资源使用
yarn application -list
```

## 最佳实践

1. **环境隔离**：为不同环境使用不同的配置文件
2. **版本控制**：将配置文件纳入版本控制
3. **文档维护**：及时更新配置文档
4. **性能测试**：定期进行性能基准测试
5. **监控告警**：设置关键指标的监控告警

## 配置模板选择指南

| 场景 | 推荐模板 | 适用规模 | 主要特点 |
|------|----------|----------|----------|
| 开发测试 | local | < 10GB | 本地运行，快速验证 |
| 生产环境 | hive | 100GB-10TB | Hive集成，企业级 |
| 大规模分析 | pb-scale | > 10TB | PB级优化，高性能 |
