# Iceberg Spark TPC-DS 基准测试工具

这是一个基于Apache Spark和Apache Iceberg的TPC-DS基准测试工具，专门针对PB级数据场景进行了优化。该工具支持灵活的配置管理，避免了硬编码配置的局限性。

## 主要特性

- ✅ **配置文件驱动**：支持通过配置文件管理所有参数，避免硬编码
- ✅ **基于Spark SQL**：使用DDL语句创建和管理Iceberg表
- ✅ **PB级优化**：针对PB级数据场景的专门优化配置
- ✅ **灵活的Iceberg配置**：支持Hadoop和Hive catalog，可配置表格式参数
- ✅ **多环境支持**：提供本地开发、生产环境、PB级场景的预定义配置
- ✅ **中文文档**：完整的中文使用文档和配置指南

## 快速开始

### 环境要求

- Apache Spark 3.2.1+
- Apache Iceberg 0.13.1+
- Java 8+
- Scala 2.12

### 安装和编译

```bash
# 克隆项目
git clone <repository-url>
cd iceberg-spark-tpcds-benchmark

# 编译项目
mvn clean package -DskipTests

# 或者使用提供的脚本
./build/mvn clean package -DskipTests
```

### 基本使用

#### 1. 本地开发环境

```bash
# 使用本地开发模板
./bin/dsdgen --template local --output-location /tmp/tpcds-data --scale-factor 1

# 运行基准测试
./bin/run-tpcds-benchmark --template local --data-location /tmp/tpcds-data
```

#### 2. 生产环境（Hive Catalog）

```bash
# 使用Hive catalog配置
./bin/dsdgen --template hive --output-location hdfs://namenode:9000/tpcds-data --scale-factor 1000

# 运行基准测试
./bin/run-tpcds-benchmark --template hive --data-location hdfs://namenode:9000/tpcds-data
```

#### 3. PB级数据场景

```bash
# 使用PB级优化配置
./bin/dsdgen --template pb-scale --output-location hdfs://namenode:9000/tpcds-pb --scale-factor 10000

# 运行基准测试
./bin/run-tpcds-benchmark --template pb-scale --data-location hdfs://namenode:9000/tpcds-pb
```

## 配置管理

### 预定义模板

项目提供了三个预定义的配置模板：

1. **local** - 本地开发环境
   - 使用local[2] master
   - Hadoop catalog，数据存储在本地文件系统
   - 小规模数据，快速测试

2. **hive** - 生产环境（Hive Catalog）
   - 使用YARN cluster模式
   - Hive Metastore catalog
   - 中等规模数据

3. **pb-scale** - PB级数据场景
   - 大规模YARN集群
   - 优化的文件大小和压缩设置
   - 启用所有性能优化选项

### 自定义配置文件

您可以创建自定义的配置文件：

```bash
# 使用自定义配置文件
./bin/dsdgen --config /path/to/your/config.properties
```

配置文件示例：

```properties
# Spark配置
spark.master=yarn
spark.executor.memory=8g
spark.executor.cores=4

# Iceberg配置
iceberg.enabled=true
iceberg.default.catalog=hadoop_prod
iceberg.catalog.hadoop_prod.type=hadoop
iceberg.catalog.hadoop_prod.warehouse=hdfs://namenode:9000/warehouse

# 数据生成配置
datagen.scale.factor=1000
datagen.output.location=hdfs://namenode:9000/tpcds-data
```

## Iceberg Catalog配置

### Hadoop Catalog

适用于不依赖Hive Metastore的场景：

```properties
iceberg.catalog.hadoop_prod.type=hadoop
iceberg.catalog.hadoop_prod.warehouse=hdfs://namenode:9000/warehouse/iceberg
iceberg.catalog.hadoop_prod.property.write.format.default=parquet
iceberg.catalog.hadoop_prod.property.write.parquet.compression-codec=zstd
```

### Hive Catalog

适用于已有Hive Metastore的环境：

```properties
iceberg.catalog.hive_prod.type=hive
iceberg.catalog.hive_prod.warehouse=hdfs://namenode:9000/warehouse/hive
iceberg.catalog.hive_prod.uri=thrift://hive-metastore:9083
```

## PB级数据优化

针对PB级数据场景，项目提供了以下优化：

### 文件大小优化
- 目标文件大小：1GB（适合大规模数据）
- Row Group大小：128MB
- Page大小：1MB

### 压缩优化
- 使用ZSTD压缩算法
- 启用字典编码
- 支持Bloom Filter

### 分区和排序优化
- 智能分区策略
- 基于查询模式的排序
- 支持Z-Order排序

### Spark优化
- 自适应查询执行（AQE）
- 动态分区合并
- 倾斜连接处理

## 命令行选项

### 数据生成器选项

```bash
./bin/dsdgen [选项]

选项:
  -c, --config <文件>          配置文件路径
  -t, --template <模板>        使用预定义模板 (local, pb-scale, hive)
  --output-location <路径>     输出数据位置
  --scale-factor <数字>        数据规模因子
  --iceberg                   启用Iceberg格式
  -h, --help                  显示帮助信息
```

### 基准测试选项

```bash
./bin/run-tpcds-benchmark [选项]

选项:
  --data-location <路径>       数据位置
  --query-filter <查询>        要运行的查询，如 q1,q2,q3
  --cbo                       启用基于成本的优化
  --iceberg                   使用Iceberg表
  --template <模板>            使用预定义模板
```

## 性能调优指南

### 内存配置

```properties
# Executor内存配置
spark.executor.memory=16g
spark.executor.memoryFraction=0.8

# Driver内存配置
spark.driver.memory=4g
spark.driver.maxResultSize=2g
```

### 并行度配置

```properties
# 分区数量
datagen.num.partitions=10000

# Executor数量
spark.executor.instances=100
spark.executor.cores=8
```

### 存储优化

```properties
# 文件大小优化
iceberg.table.property.write.target-file-size-bytes=1073741824

# 压缩优化
iceberg.table.property.write.parquet.compression-codec=zstd
```

## 监控和调试

### 启用事件日志

```properties
spark.eventLog.enabled=true
spark.eventLog.dir=hdfs://namenode:9000/spark-events
```

### 性能指标

工具会自动收集以下性能指标：
- 查询执行时间
- 数据扫描量
- 文件数量和大小
- 内存使用情况

## 故障排除

### 常见问题

1. **Catalog连接失败**
   - 检查Hive Metastore连接
   - 验证HDFS权限
   - 确认网络连通性

2. **内存不足**
   - 增加executor内存
   - 调整分区数量
   - 启用动态资源分配

3. **性能问题**
   - 启用CBO优化
   - 调整文件大小
   - 优化分区策略

### 日志配置

```properties
# 启用详细日志
log4j.logger.org.apache.iceberg=DEBUG
log4j.logger.org.apache.spark.sql.execution.benchmark=INFO
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目基于Apache License 2.0许可证开源。
